import { languages } from "@lang/langs";
import { changeLang, getLangFromUrl } from "@lang/utils";

const userLang: string = // @ts-ignore
  (navigator.language || navigator?.userLanguage).split("-")[0];

const currentLang = getLangFromUrl(window.location);

if (
  Object.keys(languages).includes(userLang) &&
  userLang !== currentLang &&
  !localStorage.getItem("lang") &&
  window.location.pathname.split("/").length <= 3
) {
  changeLang(userLang);
}
