---
import "@styles/main.scss";

//#region [Styles]
import "@styles/pages/rating-system.scss";
//#endregion [Styles]

//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
//#endregion [Components]

//#region [Images]
import RatingTreeImg from "@assets/images/rating-system/rating-tree.svg";

import ExerciseImg1 from "@assets/images/rating-system/exercises/eng-10m-1.svg";
import ExerciseImg2 from "@assets/images/rating-system/exercises/eng-serpent-2.svg";
import ExerciseImg3 from "@assets/images/rating-system/exercises/eng-jump-3.svg";
import ExerciseImg4 from "@assets/images/rating-system/exercises/eng-arrow-4.svg";

import MicroSkillsDesktopImg from "@assets/images/rating-system/micro-skills-desktop.png";
import MicroSkillsMobileImg from "@assets/images/rating-system/micro-skills-mobile.png";

const SkillsImages = await Astro.glob(
  "/src/assets/images/skills-cards/en/*.{png,svg,jpg}"
).then((files) => {
  return files.map((file) => {
    return file.default;
  });
});
//#endregion [Images]
---

<Layout>
  <!-- Hero -->
  <section id="hero">
    <div class="container">
      <div class="hero">
        <div class="hero__top">
          <h1 class="hero__title">Rating calculation</h1>
          <p class="hero__text">
            Designed to provide a comprehensive assessment of a young football
            player's abilities, with a focus on accuracy and reliability.
            <br /><br />At the top of the system is the player's overall rating,
            which is calculated as the average of all the player's skills. The
            skills, in turn, are calculated as the average of the micro-skills
            that make them up. The micro-skills are derived from raw test
            results, which are captured through Machine vision
          </p>
        </div>
        <Image
          src={RatingTreeImg}
          alt="Visualization of rating tree"
          loading="eager"
        />
      </div>
    </div>
  </section>
  <!-- Raw data -->
  <section id="raw" class="section-space">
    <div class="container">
      <div class="section-header">
        <div class="section__title">
          <h1 class="section__headline">Raw tests data</h1>
          <p class="section__title-text">
            Machine vision impartially assesses multiple parameters of completed
            tests
          </p>
        </div>
        <div class="raw__exercises-grid">
          <Image
            class="raw__exercises-grid-image"
            src={ExerciseImg1}
            alt="Exercise results"
          />
          <div class="raw__exercises-nested-col">
            <Image
              class="raw__exercises-grid-image"
              src={ExerciseImg2}
              alt="Exercise results"
            />
            <Image
              class="raw__exercises-grid-image"
              src={ExerciseImg3}
              alt="Exercise results"
            />
          </div>
          <Image
            class="raw__exercises-grid-image"
            src={ExerciseImg4}
            alt="Exercise results"
          />
        </div>
      </div>
    </div>
  </section>
  <!-- Micro-skills -->
  <section id="micro-skills" class="section-space">
    <div class="container">
      <div class="section-header">
        <div class="section__title">
          <h1 class="section__headline">Micro-skills</h1>
          <p class="section__title-text">
            Each micro-skill is calculated from multiple raw data in different
            tests
          </p>
        </div>

        <div>
          <Image
            class="micro-skills__img is--desktop"
            src={MicroSkillsDesktopImg}
            alt="Micro skills graphs"
          />
        </div>
        <div>
          <Image
            class="micro-skills__img is--mobile"
            src={MicroSkillsMobileImg}
            alt="Micro skills graphs"
          />
        </div>
      </div>
    </div>
  </section>
  <!-- Tests -->
  <section id="tests" class="section-space">
    <div class="container">
      <div class="tests">
        <div class="tests__images">
          <Image class="tests__img" src={SkillsImages[0]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[3]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[1]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[4]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[2]} alt="Skill card" />
        </div>
        <div class="section-header tests__header">
          <div class="section__title">
            <h1 class="section__headline">Skills and tests</h1>
            <p class="section__title-text">
              Agility, Physical, Pace, Shooting, Dribbling - the five defining
              skills, which are calculated from micro-skills and include a
              specific set of tests. To get a complete picture of a player's
              skills, we recommend taking all the tests. This allows you to not
              only understand the player's overall ability, but also get a clear
              picture of their proficiency in each individual skill.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>
