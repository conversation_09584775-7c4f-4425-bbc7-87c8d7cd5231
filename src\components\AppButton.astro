---
import type { HTMLTag, Polymorphic } from "astro/types";

type Props<Tag extends HTMLTag> = Polymorphic<{
  as: Tag;
  typeStyle?: "primary" | "secondary";
  style?: "mod2";
  skew?: boolean;
}>;

const { as: Tag, typeStyle, style, skew = false, ...props } = Astro.props;

const className =
  typeStyle === "secondary" ? "button--secondary" : "button--primary";
---

<Tag
  {...props}
  class:list={["button", className, style && "button--mod2", skew && "skew"]}
>
  <div><slot /></div>
</Tag>

<style lang="scss">
  @use "sass:color";
  @use "@styles/_variables.scss" as v;
  @use "@styles/_screens.scss" as screen;

  .button {
    color: v.$black;
  }

  .button > div {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    font-size: 16px;
  }

  .button--primary {
    color: v.$black;
    background-color: v.$primary;

    &:hover {
      background-color: color.adjust(v.$primary, $lightness: -15%);
    }
  }

  .button--secondary {
    color: v.$black;
    background-color: v.$white;

    &:hover {
      background-color: color.adjust(v.$white, $lightness: -15%);
    }
  }

  .button--mod2 {
    border: 1px solid #dfe5ea;
    font-weight: 700;
    position: relative;
    transition: background-size 0.27s v.$smooth-ease;
  }

  .button--primary.button--mod2 {
    background:
      linear-gradient(180deg, v.$primary 0%, #ffd53f 100%), v.$primary;
    background-size: 100% 100%;

    &:hover {
      background-size: 100% 500%;
    }
  }

  .button--secondary.button--mod2 {
    background: linear-gradient(180deg, #ffffff 0%, #f3f3f3 100%), #ffe83f;
    background-size: 100% 100%;

    &:hover {
      background-size: 100% 1000%;
    }
  }

  .skew {
    @media screen and (min-width: screen.$tablet) {
      transform: skewX(-10deg);

      & > * {
        transform: skewX(10deg);
      }
    }
  }
</style>
