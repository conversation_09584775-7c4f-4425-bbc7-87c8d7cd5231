<script lang="ts">
  export let player: any;

  import { track } from "@amplitude/analytics-browser";

  function trackCardClick() {
    track("Click player card", {
      player_id: player?.id,
      name: `${player?.firstName} ${player?.lastName}`,
    });
  }
</script>

<div class="rating__player-card-wrap">
  <a
    class="players__card"
    href="https://app.junistat.com/player/{player?.id}"
    data-playerid={player?.id}
    target="_blank"
    on:click={trackCardClick}
    rel="noopener noreferrer"
  >
    <img
      class="players__card-avatar-wrap"
      src={player?.image?.id
        ? "https://app.junistat.com/api/images/" +
          player?.image?.id +
          "/public?source=front&size=344x344"
        : "/rating/no-avatar.svg"}
      alt={player?.firstName + " " + player?.lastName}
    />
    <div class="players__card-rating h2" title="Rating">
      {player?.rating || "?"}
    </div>
    <div class="players__card-under h3" title="Age group">
      {player?.ageGroup || "?"}
    </div>
    <img
      class="players__card-logo"
      alt="Logo"
      src={player?.academyImage?.id !== undefined
        ? `https://app.junistat.com/api/images/${player?.academyImage.id}/public?source=front&size=56x56`
        : "/rating/js-circle-logo.svg"}
    />
    <img
      alt="Flag"
      class="players__card-flag"
      src="/flags/{String(
        player?.address?.countryCodeAlpha2,
      ).toLowerCase()}.svg"
    />
    <div class="players__card-name h3">
      {player?.firstName?.toUpperCase() ?? ""}<br />{player?.lastName
        ?.replace(/ .*/gm, "")
        .toUpperCase() ?? ""}
    </div>
    <img
      alt="Foot icon"
      class="players__card-foot"
      src={player?.leadingLeg === "left"
        ? "/rating/foot-left.svg"
        : "/rating/foot-right.svg"}
    />
    <div class="players__card-skills-wrap">
      <div class="players__card-skill h3">
        <abbr title="Physical">PHY</abbr><span
          class="players__card-skill-points"
          >{player?.skills?.physical?.value ?? "0"}</span
        >
      </div>
      <div class="players__card-skill h3">
        <abbr title="Pace">PAC</abbr><span class="players__card-skill-points"
          >{player?.skills?.pace?.value ?? "0"}</span
        >
      </div>
      <div class="players__card-skill h3">
        <abbr title="Agility">AGI</abbr><span class="players__card-skill-points"
          >{player?.skills?.agility?.value ?? "0"}</span
        >
      </div>
      <div class="players__card-skill h3">
        <abbr title="Dribbling">DRI</abbr><span
          class="players__card-skill-points"
          >{player?.skills?.dribbling?.value ?? "0"}</span
        >
      </div>
      <div class="players__card-skill h3">
        <abbr title="Shooting">SHO</abbr><span
          class="players__card-skill-points"
          >{player?.skills?.shooting?.value ?? "0"}</span
        >
      </div>

      <div class="players__card-skills-divider" />
    </div>
  </a>
</div>

<style>
  /*#region [Player card]*/
  .rating__player-card-wrap {
    display: flex;
    justify-content: center;
  }

  .players__card {
    display: block;
    overflow: hidden;
    width: 264px;
    height: 425px;
    background-image: url(/rating/player-card.svg);
    background-position: 0rem 0rem;
    background-repeat: no-repeat;
    background-size: contain;
    position: relative;

    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
    transition-duration: 300ms;
  }

  @media (any-hover: hover) {
    .players__card:hover {
      opacity: 0.7;
    }
  }

  .players__card-avatar-wrap {
    display: block;
    width: 11.25rem;
    height: 13.5rem;
    margin-left: auto;
    overflow: hidden;
    border-radius: 16px 16px 0 16px;
    border: #000 1px solid;
    background-color: #fff;
    object-fit: cover;
  }

  .players__card-rating {
    position: absolute;
    font-size: 2.5rem;
    width: 4rem;
    text-align: center;
    top: 2%;
    left: 5%;
    font-weight: 800;
  }

  .players__card-under {
    color: #fff;
    width: 4rem;
    position: absolute;
    font-size: 1.5rem;
    text-align: center;
    top: 14.5%;
    left: 5%;
  }

  .players__card-flag {
    display: block;
    position: absolute;
    border-radius: 4px;
    width: 2.6rem;
    top: 25%;
    left: 8.5%;
  }

  .players__card-logo {
    position: absolute;
    border-radius: 100%;
    width: 3rem;
    top: 38%;
    left: 7.5%;
  }

  .players__card-name {
    display: inline-block;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1.5rem;
    line-height: 100%;
    width: 82%;
    padding-left: 2px;
    top: 1rem;
    left: 4%;
    font-weight: 800;
  }

  .players__card-foot {
    position: absolute;
    top: 57%;
    right: 1rem;
    display: block;
    width: 2rem;
    margin-left: auto;
  }

  .players__card-skills-wrap {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    row-gap: 2px;
    margin-top: 1.7rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .players__card-skill {
    display: flex;
    justify-content: space-between;
    font-size: 1.5rem;
  }

  .players__card-skills-divider {
    grid-row: 1 / span 3;
    grid-column-start: 2;
    justify-self: center;
    width: 1px;
    background-color: #000;
    margin-right: 1rem;
    margin-left: 1rem;
  }
  /*#endregion [Player card]*/
</style>
