---
import { getCollection } from "astro:content";
import HCArticle from "@layouts/HC-Article-Layout.astro";

export async function getStaticPaths() {
  const blogEntries = await getCollection("hc");
  return blogEntries.map(
    (entry) => (
      console.log("entry :>> ", entry),
      {
        params: { slug: entry.slug },
        props: { entry },
      }
    ),
  );
}

const { entry } = Astro.props;

const { Content } = await entry.render();
---

<HCArticle frontmatter={entry.data}>
  <Content />
</HCArticle>
