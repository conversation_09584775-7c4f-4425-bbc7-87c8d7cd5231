@use "sass:color";
@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Hero]*/
.hero {
  margin-top: $space-48;
}

.hero__title {
  width: 70%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.hero__video {
  margin-top: $space-48;
}

.hero__video-tag {
  background-size: cover;
}

.hero__video {
  border-radius: 1rem;
  overflow: hidden;
}

.video {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 10;
  overflow: hidden;
  border-radius: 16px;
  isolation: isolate;
}

.video__play-button {
  display: none;
  width: 80px;
  height: 80px;
  position: absolute;
  z-index: 15;
  pointer-events: none;
}

video {
  width: 100%;
}

.hero__quote-block {
  display: flex;
  align-items: flex-start;
  max-width: 84%;
  margin-top: $space-32;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    gap: $space-24;
  }
}

.hero__quote-text {
}

.hero__quote-author {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: $space-24;

  @media screen and (max-width: $mobile) {
    margin-left: 0;
  }
}

.hero__author-avatar {
  width: $space-56;
  height: $space-56;
  margin-right: $space-16;
  overflow: hidden;
  border-radius: 100%;
}

.download-buttons {
  display: flex;
  gap: $space-16;
  margin-top: $space-32;

  & img {
    max-height: $space-64;
  }

  @media screen and (max-width: $mobile) {
    margin-top: $space-48;
  }
}

.download-button {
  @include transition-opacity;
  &:hover {
    opacity: 60%;
  }
}

.hero__video {
  border-radius: 1rem;
  overflow: hidden;
}
/*#endregion [Hero]*/

/*#region [About]*/

.about__grid {
  display: grid;
  grid-template-columns: 0.75fr 1fr;
  column-gap: $space-128;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column-reverse;
    row-gap: $space-24;
  }

  &:last-child {
    margin-top: $space-56;
  }
}

.about__profiles-cards {
  position: relative;
  max-width: 24rem;

  @media screen and (max-width: $mobile) {
    max-width: 75%;
  }
}

.about__profiles-card {
  position: relative;
  width: 100%;
  z-index: 1;
  top: 0;

  &:not(:first-child) {
    position: absolute;
    transform-origin: center bottom;
  }

  &:nth-child(1) {
    z-index: 12;
  }
  &:nth-child(2) {
    z-index: 11;
    transform: rotateZ(10deg) translateY(-2rem) scale(0.9);
  }
  &:nth-child(3) {
    z-index: 10;
    transform: rotateZ(20deg) translateY(-4rem) scale(0.8);
  }
}

.about__profiles-info {
  align-self: center;
  max-width: 35rem;
}

.about__profiles-info-title {
  font-weight: 700;

  @media screen and (min-width: $desktop) {
    font-size: 3rem;
  }
}

.about__skills {
  grid-template-columns: 1fr 0.75fr;

  @media screen and (max-width: $mobile) {
    flex-direction: column;
  }
}

.about__skills-cards {
  position: relative;
  max-width: 24rem;

  @media screen and (max-width: $mobile) {
    max-width: 75%;
  }
}

.about__skills-card {
  display: block;
  position: relative;
  width: 100%;
  z-index: 1;
  top: 0;
  border-radius: $space-16;
  overflow: hidden;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.06), 0px 4px 16px rgba(0, 0, 0, 0.05);

  &:nth-child(1) {
    z-index: 10;
  }
  &:nth-child(2) {
    z-index: 11;
    margin-top: -60%;
    transform: translateX(30%);
  }
  &:nth-child(3) {
    z-index: 12;
    margin-top: -50%;
    transform: translateX(15%);
  }
}

.about__skills-info {
  align-self: center;
  max-width: 40rem;
}

.about__skills-info-title {
  font-weight: 700;

  @media screen and (min-width: $desktop) {
    font-size: 3rem;
  }
}
.about__skills-info-text {
  margin-top: $space-16;
  max-width: 28rem;
}
/*#endregion [About]*/

/*#region [Chart]*/
#chart .container {
  color: $white;
  background-color: $black;
  border-radius: $space-24;
  padding: 0;
}

.chart {
  display: flex;
  flex-direction: column;
  gap: $space-56;
  text-align: center;
  padding: $space-56 $space-56;
  overflow: hidden;

  @media screen and (max-width: $tablet) {
    padding: $space-32 0;
  }
}

.chart__title {
  line-height: 1.25em;

  @media screen and (min-width: $desktop) {
    font-size: 3rem;
  }

  @media screen and (max-width: $tablet) {
    padding: 0 $space-32;
  }
}

.chart__graph {
  @media screen and (max-width: $tablet) {
    width: 200%;
    max-width: none;
  }
}
/*#endregion [Chart]*/

/*#region [Footer cards]*/
.footer-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.footer-cards__card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: $space-24;
  text-align: center;
  gap: $space-24;

  background-color: $white;
  border-radius: $space-8;
  border: 1px solid $gray;

  @include transition-colors;

  &:hover {
    background-color: color.change($white, $lightness: 93%);
  }

  &.is--material {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    @media screen and (max-width: $mobile) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &.is--accent {
    background-color: $primary;
  }
}

.footer-cards__card-side {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: $space-8;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-text {
  width: 80%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-icon {
  width: $space-64;
  height: $space-64;
}

.download-buttons.is--footer {
  justify-content: center;
  align-items: center;
  margin-top: 0;
  flex-wrap: wrap;
}
/*#endregion [Footer cards]*/
