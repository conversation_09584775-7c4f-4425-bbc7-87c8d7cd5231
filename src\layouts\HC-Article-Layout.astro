---
import "@styles/main.scss";
import { Image, Picture, getImage } from "astro:assets";
import Layout from "@layouts/Layout.astro";
import AppButton from "@components/AppButton.astro";

//#region [i18n]
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "@lang/utils.ts";

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]

const images = import.meta.glob<{ default: ImageMetadata }>(
  "./_images/*.{jpeg,jpg,png,gif}",
);
---

<Layout>
  <div class="container article-container">
    <div class="content">
      <div class="header">
        <AppButton
          as="a"
          typeStyle="secondary"
          class="back-button"
          href={translatePath("/hc/")}
        >
          {t("help_center.back_button")}
        </AppButton>
      </div>
      <div class="content-wrap">
        <slot />
      </div>
    </div>
    <aside class="sidebar"></aside>
  </div>
</Layout>

<script>
  // Select all h2 elements
  const h2Elements = document.querySelectorAll("h2");
  // Create an unordered list
  const ul = document.createElement("ul");

  h2Elements.forEach((h2, index) => {
    // Generate a unique ID for each h2
    const id = `h2-${index}`;
    h2.setAttribute("id", id);

    // Create a list item and an anchor
    const li = document.createElement("li");
    const a = document.createElement("a");
    a.href = `#${id}`;
    a.textContent = h2.textContent;

    // Append the anchor to the list item and the list item to the ul
    li.appendChild(a);
    ul.appendChild(li);
  });

  // Append the ul to the aside element
  document.querySelector(".sidebar")?.appendChild(ul);
</script>

<style is:global>
  p,
  h1,
  h2,
  h3 {
    max-width: 640px;
  }

  container p {
    font-size: 18px;
  }

  li {
    line-height: 120%;
  }

  ul > li + li {
    margin-top: 12px;
  }

  li > a:hover {
    color: var(--blue);
  }

  .article-container {
    margin-top: 48px;
    display: flex;
    gap: 32px;
    align-items: flex-start;
    justify-items: flex-start;
  }

  .sidebar {
    position: sticky;
    top: 24px;
    flex: 0 0 269px;
  }

  @media screen and (max-width: 992px) {
    .sidebar {
      display: none;
    }
  }

  .back-button {
    max-width: max-content;
    margin-bottom: 32px;
  }

  .content-wrap > * + * {
    margin-top: 24px;
  }

  h2 {
    font-size: 32px;
    margin-top: 48px !important;
  }

  .headline {
    font-size: 40px;
  }

  .image-wrap {
    padding: 24px;
    border-radius: 16px;
    border: 2px solid #62b3fe;
    background: #e7f0f7;
    overflow: hidden;
  }

  .image-wrap img {
    border-radius: 8px;
    overflow: hidden;
  }

  .cols-3 {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
  }

  .cols-3 > * {
    flex: 1 0 30%;
  }

  @media screen and (max-width: 688px) {
    .cols-3 {
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      gap: 32px;
    }
  }
</style>
