---
//#region [Built-in components]
import { Image } from "astro:assets";
//#endregion [Built-in components]

//#region [Images]
import PaceIcon from "@assets/images/ai-test/category-icons/pace.svg";
import AgilityIcon from "@assets/images/ai-test/category-icons/agility.svg";
import DribblingIcon from "@assets/images/ai-test/category-icons/dribbling.svg";
import ShootingIcon from "@assets/images/ai-test/category-icons/shooting.svg";
import PhysicalIcon from "@assets/images/ai-test/category-icons/physical.svg";
//#endregion [Images]

interface Props {
  lang?: "ru" | "en";
  type: "pace" | "agility" | "dribbling" | "shooting" | "physical";
}

const { type, lang } = Astro.props;

let icon: any = null;
let name: string | null = null;

if (lang === "ru") {
  (function () {
    switch (type) {
      case "pace":
        icon = PaceIcon;
        name = "Быстрота";
        return;
      case "agility":
        icon = AgilityIcon;
        name = "Ловкость";
        return;
      case "dribbling":
        icon = DribblingIcon;
        name = "Дриблинг";
        return;
      case "shooting":
        icon = ShootingIcon;
        name = "Удары";
        return;
      case "physical":
        icon = PhysicalIcon;
        name = "Атлетизм";
        return;
    }
  })();
} else {
  (function () {
    switch (type) {
      case "pace":
        icon = PaceIcon;
        name = "Pace";
        return;
      case "agility":
        icon = AgilityIcon;
        name = "Agility";
        return;
      case "dribbling":
        icon = DribblingIcon;
        name = "Dribbling";
        return;
      case "shooting":
        icon = ShootingIcon;
        name = "Shooting";
        return;
      case "physical":
        icon = PhysicalIcon;
        name = "Physical";
        return;
    }
  })();
}
---

<div class="tag">
  <Image class="tag-icon" src={icon} alt={name} width={32} />
  <div class="tag-name">{name}</div>
</div>

<style>
  .tag {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border-radius: 8px;
    background-color: #f4f5f9;
    column-gap: 8px;
  }

  @media screen and (max-width: 688px) {
    .tag {
      font-size: 14px;
    }
  }

  .tag-icon {
    width: 32px;
  }
</style>
