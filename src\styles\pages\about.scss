@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Hero]*/
.hero {
  margin-top: $space-48;
}

.hero__title {
  width: 90%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.hero__title-subtitle {
  margin-top: $space-24;
  max-width: 720px;
}

.hero__copy {
  margin-top: $space-48;
  max-width: 43.75rem;
  line-height: 1.35em;
}

/*#endregion [Hero]*/

/*#region [Investors]*/
.investors__content {
  display: flex;
  align-items: center;
  gap: $space-32;
  margin-top: $space-48;

  @media screen and (max-width: $mobile) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.investors__cite {
  overflow: hidden;
  max-width: 25rem;
  padding: $space-16;
  background-color: $white;
  border-radius: $space-16;
}

.investors__cite-footer {
  display: flex;
  align-items: center;
  column-gap: $space-8;
  margin-top: $space-16;
}

.investors__cite-avatar {
  overflow: hidden;
  width: 3rem;
  height: 3rem;
  border-radius: 100%;
}

.investors__logos {
  display: flex;
  align-items: center;
  gap: $space-32;
}

.investors__investor-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 140px;
  row-gap: $space-8;

  @include transition-opacity;

  &:hover {
    opacity: 0.5;
  }
}

.investors__logo {
  height: 80px;
}
/*#endregion [Investors]*/

/*#region [Media]*/
#media {
  overflow: visible;
}

.media__title {
  margin-bottom: $space-48;
}

.media__content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $space-24;

  @media screen and (max-width: 860px) {
    grid-template-columns: repeat(3, 1fr);
  }
  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
    gap: $space-16;
    align-items: stretch;
  }
}

.media__card {
  display: flex;
  flex-direction: column;
  padding: $space-16;
  background-color: $white;
  overflow: hidden;
  border-radius: $space-16;
  max-width: 360px;
  justify-content: space-between;

  @include transition-all;

  &:hover {
    background-color: $primary;
    box-shadow: 0 10px 20px -16px rgba(0, 0, 0, 0.32);
  }

  @media screen and (max-width: $mobile) {
    max-width: 100%;
  }
}

.media__card-link {
  margin-top: $space-8;
}

/*#endregion [Media]*/

/*#region [Partners]*/
.partners__title {
  margin-bottom: $space-48;
}

.partners__logos {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: $space-24;

  @media screen and (max-width: $tablet) {
    grid-template-columns: repeat(3, 1fr);
  }
  @media screen and (max-width: $mobile) {
    grid-template-columns: repeat(2, 1fr);
  }
}
/*#endregion [Partners]*/
