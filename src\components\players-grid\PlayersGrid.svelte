<script lang="ts">
  import { track } from "@amplitude/analytics-browser";

  //#region [i18n]
  import {
    getLangFromUrl,
    useTranslations,
    useTranslatedPath,
  } from "@lang/utils";

  const lang = getLangFromUrl(window.location);
  const t = useTranslations(lang);
  //#endregion [i18n]

  //#region [Components]
  import debounce from "@src/utils/debounce";
  import PlayerCard from "@components/players-grid/PlayerCard.svelte";
  //#endregion [Components]

  //#region [Props]
  import { franchise } from "./academyStore";

  //#endregion [Props]

  let page: number = 0;
  let totalCount: number = 0;
  let pageSize: number = 8;

  function getPagesize() {
    const isMobile = window.matchMedia("(max-width: 688px)").matches;
    if (isMobile) {
      pageSize = 2;
    } else {
      pageSize = 8;
    }
  }

  getPagesize();
  window.addEventListener("resize", debounce(getPagesize, 400));

  const trackSearchPlayer = debounce(
    () =>
      track("Search player", {
        name: searchName,
      }),
    1000,
  );

  //#region [Search]
  let searchName: string | null = null;
  let searchNameOld: string | null = null;

  $: if (searchName !== searchNameOld) {
    searchNameOld = searchName;
    page = 0;
    refreshPlayers();

    // Track search player
    if (searchName !== "") {
      trackSearchPlayer();
    }
  }

  //#endregion [Search]

  //#region [Get players]
  let controller = new AbortController();
  let signal = controller.signal;
  let firstLoad = true;

  let fetchPlayersPromise = fetchPlayers();

  function refreshPlayers() {
    if (firstLoad) {
      firstLoad = false;
    } else {
      controller.abort();
      fetchPlayersPromise = fetchPlayers();
    }
  }

  $: $franchise &&
    (function () {
      page = 0;
      refreshPlayers();
    })();

  async function fetchPlayers() {
    // Controller to cancel previous fetch
    controller = new AbortController();
    signal = controller.signal;
    let url = `https://app.junistat.com/api/public/players?limit=${pageSize}&offset=${page}`;

    if ($franchise.id) {
      url += `&franchise=${$franchise.id}`;
    }

    if ($franchise.academyId) {
      url += `&academy=${$franchise.academyId}`;
    }

    if (searchName !== null && searchName !== "") {
      url += `&name=${searchName}`;
    }

    try {
      const response = await fetch(url, { signal });
      const {
        data,
        meta: { count },
      } = await response.json();
      totalCount = count;
      return data;
    } catch (error) {}
  }

  function trackUsePagination(prev = false) {
    track("Use pagination", {
      prev_page: prev,
    });
  }

  function nextPage() {
    page += pageSize;
    refreshPlayers();
    trackUsePagination();
  }

  function prevPage() {
    if (page < pageSize) return;
    page -= pageSize;
    refreshPlayers();
    trackUsePagination(true);
  }

  //#endregion [Get players]
</script>

<div class="rating__header">
  {#if !$$slots.selector}
    <h2>{t("rating.titleAllPlayers")}</h2>
  {/if}
  <slot name="selector" />
  <div class="rating__header-filters">
    <!-- <div class="rating__filter-button button">
      <svg class="search__input-icon is--relative" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" fill="none" viewBox="0 0 32 32"><path fill="#181B1E" d="M8 13.125V5a1 1 0 0 0-2 0v8.125a4 4 0 0 0 0 7.75V27a1 1 0 1 0 2 0v-6.125a4 4 0 0 0 0-7.75ZM7 19a2 2 0 1 1 0-4 2 2 0 0 1 0 4ZM17 7.125V5a1 1 0 1 0-2 0v2.125a4 4 0 0 0 0 7.75V27a1 1 0 0 0 2 0V14.875a4 4 0 0 0 0-7.75ZM16 13a2 2 0 1 1 0-4 2 2 0 0 1 0 4Zm13 8a4.008 4.008 0 0 0-3-3.875V5a1 1 0 1 0-2 0v12.125a4 4 0 0 0 0 7.75V27a1 1 0 0 0 2 0v-2.125A4.007 4.007 0 0 0 29 21Zm-4 2a2 2 0 1 1 0-4 2 2 0 0 1 0 4Z"/></svg>
      {t("rating.filters")}
    </div> -->
    <label class="search__input-wrap" for="search_by_name">
      <!-- prettier-ignore -->
      <svg  class="search__input-icon {searchName ? 'search__input-icon--active' : null}"  xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M10 2.5a7.5 7.5 0 0 1 5.964 12.048l4.743 4.745a1 1 0 0 1-1.32 1.497l-.094-.083-4.745-4.743A7.5 7.5 0 1 1 10 2.5Zm0 2a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11Z"/></svg>

      <input
        class="search__input"
        id="search_by_name"
        type="search"
        name="player"
        minlength="2"
        placeholder={t("rating.search_placeholder")}
        bind:value={searchName}
      />
    </label>
    <!-- <select class="rating__academy-select" name="academy" id="select-academy">
      <option value="club" selected disabled>
        {t("rating.select_club")}
      </option>
      <option> Academies </option>
    </select> -->
  </div>
</div>

<svg class="svg" style="position:absolute; opacity:0; pointer-events: none;">
  <clipPath id="shield-mask" clipPathUnits="objectBoundingBox"
    ><path
      d="m0.018,0.946,0,0.001,0,0,0,-0.001 m0,0,0,0.001,0,0,0,0,0,0,0.001,0,0.002,0 a2,1,0,0,0,0.035,0.005 c0.023,0.003,0.053,0.007,0.085,0.011 l0,-0.001,0,0.001,0.262,0.029,0,0 c0.049,0.006,0.074,0.008,0.099,0.008 c0.025,0,0.05,-0.003,0.099,-0.008 l0,0,0.26,-0.029 c0.031,-0.004,0.062,-0.007,0.084,-0.011 a2,1,0,0,0,0.035,-0.005 l0.002,0,0.001,0,0,0 h0 l0,0,0,0,0.001,0 c0.001,0,0.002,0,0.003,0 c0.003,0,0.006,-0.001,0.009,-0.002 l0,0,0,0 a0.017,0.011,0,0,0,0.005,-0.007 V0.049 C1,0.023,0.967,0.001,0.925,0.001 H0.079 C0.037,0.001,0.002,0.023,0.002,0.049 v0.888 c0,0.003,0.002,0.005,0.005,0.007 c0.001,0.001,0.002,0.001,0.003,0.001 c0.001,0,0.002,0.001,0.004,0.001 a0.045,0.028,0,0,0,0.004,0.001 l0,0,0,0,0,0,0,0,0,-0.001"
    /></clipPath
  >
</svg>

<div class="rating__grid">
  {#await fetchPlayersPromise}
    {#each Array(pageSize) as i}
      <div class="shield-placeholder" />
    {/each}
  {:then players}
    {#if totalCount > 0}
      {#each players as player}
        <PlayerCard {player} />
      {/each}
    {:else}
      <div class="rating__empty">
        <!-- prettier-ignore -->
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" fill="#000000" viewBox="0 0 256 256"><path d="M245,110.64A16,16,0,0,0,232,104H216V88a16,16,0,0,0-16-16H130.67L102.94,51.2a16.14,16.14,0,0,0-9.6-3.2H40A16,16,0,0,0,24,64V208h0a8,8,0,0,0,8,8H211.1a8,8,0,0,0,7.59-5.47l28.49-85.47A16.05,16.05,0,0,0,245,110.64ZM93.34,64l27.73,20.8a16.12,16.12,0,0,0,9.6,3.2H200v16H146.43a16,16,0,0,0-8.88,2.69l-20,13.31H69.42a15.94,15.94,0,0,0-14.86,10.06L40,166.46V64Zm112,136H43.82l25.6-64h48.16a16,16,0,0,0,8.88-2.69l20-13.31H232Z"></path></svg>

        <h3 class="rating__empty-title">{t("rating.grid.empty")}</h3>
      </div>
    {/if}
  {/await}
</div>
<!-- #region [Pagination] -->
{#if totalCount > 0}
  <div class="rating__pagination-wrap">
    <div class="rating__pagination">
      <button
        class="button rating__pagination-button"
        disabled={page < pageSize}
        on:click={prevPage}>{t("rating.grid.prev")}</button
      >
      <p class="rating__pagination-counter p2">
        {Math.floor(page / pageSize) + 1} /
        {Math.max(0, Math.ceil((totalCount - pageSize) / pageSize)) + 1}
      </p>
      <button
        class="button rating__pagination-button button--primary"
        on:click={nextPage}
        disabled={Math.ceil((totalCount - pageSize) / pageSize) <
          Math.ceil(page / pageSize) + 1}>{t("rating.grid.next")}</button
      >
    </div>
  </div>
{/if}

<!-- #endregion [Pagination] -->

<style>
  .shield-placeholder {
    display: flex;
    justify-content: center;
    width: 264px;
    height: 425px;
    clip-path: url(#shield-mask);
    background-color: rgba(0, 0, 0, 0.1);
    background-size: cover;
    background-image: linear-gradient(
      146deg,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.5) 50%,
      rgba(255, 255, 255, 0) 70%
    );
    background-position-y: 100%;
    background-size: 100% 200%;
    animation: shield-loader 1s linear infinite reverse;
  }

  @keyframes shield-loader {
    0% {
      background-position-y: 100%;
    }
    100% {
      background-position-y: 300%;
    }
  }

  .rating__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 3rem 4.5rem;
    justify-content: center;
    align-items: center;
    justify-items: center;
  }

  @media screen and (max-width: 1280px) {
    .rating__grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 3rem 3.5rem;
    }
  }

  @media screen and (max-width: 992px) {
    .rating__grid {
      grid-template-columns: 1fr 1fr;
    }
  }

  @media screen and (max-width: 600px) {
    .rating__grid {
      grid-template-columns: 1fr;
      row-gap: 2rem;
    }
  }

  /*#region [Pagination]*/
  .rating__pagination-wrap {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    min-height: 48px;
    margin-top: 32px;
  }

  .rating__pagination {
    display: flex;
    gap: 24px;
    justify-content: center;
    align-items: center;
    background: rgba(217, 217, 217, 0.7);
    border: 1px solid #f1f1f1;
    border-radius: 8px;
    padding: 0.25rem;
  }

  .rating__pagination-button {
    background: rgba(255, 255, 255, 0.8);
  }

  @media screen and (min-width: 1280px) {
    .rating__pagination-button:hover {
      background: rgba(255, 255, 255, 1);
    }
  }

  .rating__pagination-counter {
    white-space: nowrap;
  }
  /*#endregion [Pagination]*/

  /*#region [Empty state]*/
  .rating__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    grid-column: 1 / -1;
  }
  /*#endregion [Empty state]*/
</style>
