type Test = {
  [testName: string]: {
    propLabel: string;
    value: string;
    benchmarkValue: string;
    playerBarPosition: number;
    average: number;
  }[];
};

const enTests: Test = {
  "15m Sprint": [
    {
      propLabel: "Total time",
      value: "2.68 sec",
      benchmarkValue: "2.83 sec",
      playerBarPosition: 90,
      average: 74,
    },
    {
      propLabel: "Max. speed 15m",
      value: "7.29 m/sec",
      benchmarkValue: "6.89 m/sec",
      playerBarPosition: 100,
      average: 67,
    },
    {
      propLabel: "Reaction time",
      value: "0.42 sec",
      benchmarkValue: "0.40 sec",
      playerBarPosition: 70,
      average: 52,
    },
    {
      propLabel: "10m sprint time",
      value: "1.89 sec",
      benchmarkValue: "1.98 sec",
      playerBarPosition: 91,
      average: 60,
    },
    {
      propLabel: "Acceleration distance 15m",
      value: "14.66 m",
      benchmarkValue: "14.79 m",
      playerBarPosition: 95,
      average: 70,
    },
    {
      propLabel: "Starting speed",
      value: "5.25 m/sec",
      benchmarkValue: "5.08 m/sec",
      playerBarPosition: 100,
      average: 48,
    },
  ],
  "Coordination ladder left-right": [
    {
      propLabel: "Oversteps in 10sec",
      value: "25 unit",
      benchmarkValue: "23 unit",
      playerBarPosition: 100,
      average: 58,
    },
  ],
  "Coordination ladder back-forth": [
    {
      propLabel: "Oversteps in 10sec",
      value: "35 unit",
      benchmarkValue: "32 unit",
      playerBarPosition: 100,
      average: 50,
    },
  ],
  "High jump": [
    {
      propLabel: "Jump height",
      value: "30.90 cm",
      benchmarkValue: "33.80 cm",
      playerBarPosition: 80,
      average: 52,
    },
    {
      propLabel: "Landing accuracy",
      value: "95.00 %",
      benchmarkValue: "99.00 %",
      playerBarPosition: 70,
      average: 42,
    },
    {
      propLabel: "Balance",
      value: "0.97 sec",
      benchmarkValue: "0.64 sec",
      playerBarPosition: 86,
      average: 78,
    },
  ],
  Serpent: [
    {
      propLabel: "Speed dribbling",
      value: "22.93 sec",
      benchmarkValue: "22.93 sec",
      playerBarPosition: 100,
      average: 78,
    },
  ],
  "Power kick. RF/LF": [
    {
      propLabel: "Kick power",
      value: "83.32 km/h",
      benchmarkValue: "69.17 km/h",
      playerBarPosition: 100,
      average: 49,
    },
  ],
  "15m sprint from run up": [
    {
      propLabel: "Distance speed",
      value: "8.29 m/sec",
      benchmarkValue: "8.38 m/sec",
      playerBarPosition: 83,
      average: 67,
    },
    {
      propLabel: "Total time",
      value: "1.81 sec",
      benchmarkValue: "1.79 sec",
      playerBarPosition: 86,
      average: 74,
    },
    {
      propLabel: "Max. speed",
      value: "9.34 m/sec",
      benchmarkValue: "9.21 m/sec",
      playerBarPosition: 100,
      average: 48,
    },
    {
      propLabel: "Min. speed",
      value: "6.71 m/sec",
      benchmarkValue: "8.10 m/sec",
      playerBarPosition: 45,
      average: 71,
    },
  ],
  Arrow: [
    {
      propLabel: "Distance time",
      value: "10.94 sec",
      benchmarkValue: "10.46 sec",
      playerBarPosition: 70,
      average: 64,
    },
    {
      propLabel: "Lap 1",
      value: "5.47 sec",
      benchmarkValue: "5.33 sec",
      playerBarPosition: 78,
      average: 61,
    },
    {
      propLabel: "Lap 2",
      value: "4.87 sec",
      benchmarkValue: "4.63 sec",
      playerBarPosition: 60,
      average: 51,
    },
    {
      propLabel: "Turnaround time",
      value: "0.60 sec",
      benchmarkValue: "0.20 sec",
      playerBarPosition: 35,
      average: 62,
    },
  ],
  "Dribbling 15m with run up. RF/LF": [
    {
      propLabel: "Ball control",
      value: "100",
      benchmarkValue: "100",
      playerBarPosition: 100,
      average: 80,
    },
    {
      propLabel: "Total time",
      value: "2.62 sec",
      benchmarkValue: "2.54 sec",
      playerBarPosition: 90,
      average: 64,
    },
    {
      propLabel: "Ball control quality",
      value: "100%",
      benchmarkValue: "100%",
      playerBarPosition: 100,
      average: 75,
    },
    {
      propLabel: "Max. dribbling speed",
      value: "6.35 m/sec",
      benchmarkValue: "6.68 m/sec",
      playerBarPosition: 76,
      average: 42,
    },
    {
      propLabel: "Min. dribbling speed",
      value: "4.44 m/sec",
      benchmarkValue: "5.28 m/sec",
      playerBarPosition: 59,
      average: 51,
    },
  ],
  "Arrow with the ball. RF/LF": [
    {
      propLabel: "Distance time",
      value: "15.86 sec",
      benchmarkValue: "15.23 sec",
      playerBarPosition: 80,
      average: 60,
    },
    {
      propLabel: "Lap 1",
      value: "8.03 sec",
      benchmarkValue: "7.37 sec",
      playerBarPosition: 75,
      average: 50,
    },
    {
      propLabel: "Lap 2",
      value: "7.13 sec",
      benchmarkValue: "6.57 sec",
      playerBarPosition: 80,
      average: 70,
    },
    {
      propLabel: "Turnaround time",
      value: "0.70 sec",
      benchmarkValue: "0.50 sec",
      playerBarPosition: 76,
      average: 59,
    },
  ],
  "Push ups": [
    {
      propLabel: "Strength",
      value: "33.08",
      benchmarkValue: "40.37",
      playerBarPosition: 74,
      average: 56,
    },
    {
      propLabel: "Push-ups in 30sec",
      value: "38 unit",
      benchmarkValue: "42 unit",
      playerBarPosition: 80,
      average: 50,
    },
    {
      propLabel: "Push up quality",
      value: "0.44",
      benchmarkValue: "0.64",
      playerBarPosition: 40,
      average: 50,
    },
  ],
};

const ruTests: Test = {
  "Бег 15м со старта": [
    {
      propLabel: "Общее время",
      value: "2.68 сек",
      benchmarkValue: "2.83 сек",
      playerBarPosition: 90,
      average: 74,
    },
    {
      propLabel: "Макс. скорость 15м",
      value: "7.29 м/сек",
      benchmarkValue: "6.89 м/сек",
      playerBarPosition: 100,
      average: 67,
    },
    {
      propLabel: "Время реакции",
      value: "0.42 сек",
      benchmarkValue: "0.40 сек",
      playerBarPosition: 70,
      average: 52,
    },
    {
      propLabel: "Время рывка 10 м",
      value: "1.89 сек",
      benchmarkValue: "1.98 сек",
      playerBarPosition: 91,
      average: 60,
    },
    {
      propLabel: "Дистанция разгона 15м",
      value: "14.66 m",
      benchmarkValue: "14.79 m",
      playerBarPosition: 95,
      average: 70,
    },
    {
      propLabel: "Стартовая скорость",
      value: "5.25 м/сек",
      benchmarkValue: "5.08 м/сек",
      playerBarPosition: 100,
      average: 48,
    },
  ],
  "Лесенка лево-право": [
    {
      propLabel: "Количество повторений",
      value: "25 шт",
      benchmarkValue: "23 шт",
      playerBarPosition: 100,
      average: 58,
    },
  ],
  "Лесенка вперед-назад": [
    {
      propLabel: "Количество повторений",
      value: "35 шт",
      benchmarkValue: "32 шт",
      playerBarPosition: 100,
      average: 50,
    },
  ],
  "Прыжок вверх с места": [
    {
      propLabel: "Высота прыжка",
      value: "30.90 cm",
      benchmarkValue: "33.80 cm",
      playerBarPosition: 80,
      average: 52,
    },
    {
      propLabel: "Точность приземления",
      value: "95.00 %",
      benchmarkValue: "99.00 %",
      playerBarPosition: 70,
      average: 42,
    },
    {
      propLabel: "Баланс",
      value: "0.97 сек",
      benchmarkValue: "0.64 сек",
      playerBarPosition: 86,
      average: 78,
    },
  ],
  Змейка: [
    {
      propLabel: "Дриблинг на скорости",
      value: "22.93 сек",
      benchmarkValue: "22.93 сек",
      playerBarPosition: 100,
      average: 78,
    },
  ],
  "Пушечный удар правой/левой ногой": [
    {
      propLabel: "Сила удара правой ногой",
      value: "83.32 km/h",
      benchmarkValue: "69.17 km/h",
      playerBarPosition: 100,
      average: 49,
    },
  ],
  "Бег на максимальной скорости 15м": [
    {
      propLabel: "Дистанционная скорость",
      value: "8.29 м/сек",
      benchmarkValue: "8.38 м/сек",
      playerBarPosition: 83,
      average: 67,
    },
    {
      propLabel: "Общее время",
      value: "1.81 сек",
      benchmarkValue: "1.79 сек",
      playerBarPosition: 86,
      average: 74,
    },
    {
      propLabel: "Макс. скорость",
      value: "9.34 м/сек",
      benchmarkValue: "9.21 м/сек",
      playerBarPosition: 100,
      average: 48,
    },
    {
      propLabel: "Мин. скорость",
      value: "6.71 м/сек",
      benchmarkValue: "8.10 м/сек",
      playerBarPosition: 45,
      average: 71,
    },
  ],
  Стрела: [
    {
      propLabel: "Время дистанции",
      value: "10.94 сек",
      benchmarkValue: "10.46 сек",
      playerBarPosition: 70,
      average: 64,
    },
    {
      propLabel: "Круг 1",
      value: "5.47 сек",
      benchmarkValue: "5.33 сек",
      playerBarPosition: 78,
      average: 61,
    },
    {
      propLabel: "Круг 2",
      value: "4.87 сек",
      benchmarkValue: "4.63 сек",
      playerBarPosition: 60,
      average: 51,
    },
    {
      propLabel: "Разворот",
      value: "0.60 сек",
      benchmarkValue: "0.20 сек",
      playerBarPosition: 35,
      average: 62,
    },
  ],
  "Ведение 15м правой/левой с ход": [
    {
      propLabel: "Контроль мяча ПН",
      value: "100",
      benchmarkValue: "100",
      playerBarPosition: 100,
      average: 80,
    },
    {
      propLabel: "Общее время",
      value: "2.62 сек",
      benchmarkValue: "2.54 сек",
      playerBarPosition: 90,
      average: 64,
    },
    {
      propLabel: "Качество контроля",
      value: "100%",
      benchmarkValue: "100%",
      playerBarPosition: 100,
      average: 75,
    },
    {
      propLabel: "Макс. скорость с мячом",
      value: "6.35 м/сек",
      benchmarkValue: "6.68 м/сек",
      playerBarPosition: 76,
      average: 42,
    },
    {
      propLabel: "Мин. скорость с мячом",
      value: "4.44 м/сек",
      benchmarkValue: "5.28 м/сек",
      playerBarPosition: 59,
      average: 51,
    },
  ],
  "Стрела с мячом правой/левой ногой": [
    {
      propLabel: "Время дистанции",
      value: "15.86 сек",
      benchmarkValue: "15.23 сек",
      playerBarPosition: 80,
      average: 60,
    },
    {
      propLabel: "Круг 1",
      value: "8.03 сек",
      benchmarkValue: "7.37 сек",
      playerBarPosition: 75,
      average: 50,
    },
    {
      propLabel: "Круг 2",
      value: "7.13 сек",
      benchmarkValue: "6.57 сек",
      playerBarPosition: 80,
      average: 70,
    },
    {
      propLabel: "Разворот",
      value: "0.70 сек",
      benchmarkValue: "0.50 сек",
      playerBarPosition: 76,
      average: 59,
    },
  ],
  Отжимания: [
    {
      propLabel: "Сила",
      value: "33.08",
      benchmarkValue: "40.37",
      playerBarPosition: 74,
      average: 56,
    },
    {
      propLabel: "Количество отжиманий",
      value: "38 шт",
      benchmarkValue: "42 шт",
      playerBarPosition: 80,
      average: 50,
    },
    {
      propLabel: "Качество отжиманий",
      value: "0.44",
      benchmarkValue: "0.64",
      playerBarPosition: 40,
      average: 50,
    },
  ],
};

const Tests: Test = { ...enTests, ...ruTests };

export default Tests;
