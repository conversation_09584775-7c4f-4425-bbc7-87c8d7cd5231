---
import "@styles/main.scss";
import { getCollection } from "astro:content";
import Layout from "@layouts/Layout.astro";
import { Image } from "astro:assets";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils.ts";

const autoLang = getLangFromUrl(Astro.url);
const t = useTranslations(autoLang);
//#endregion [i18n]

const lang = getLangFromUrl(Astro.url);
const collectionPath: string = lang === "en" ? "blog" : `blog-${lang}`;

console.log("collectionPath :>> ", collectionPath);
// @ts-ignore
const posts = (await getCollection(collectionPath)).sort(
  (b, a) => a.data.date.valueOf() - b.data.date.valueOf(),
);
---

<Layout>
  <div class="container articles">
    <h1>{t("blog.title")}</h1>
    <ul class="posts-grid">
      {
        posts.map((post) => {
          return (
            <li>
              <a href={post.slug}>
                <Image
                  class={"posts__cover"}
                  src={post.data.cover}
                  alt={post.data.title}
                />
                <div class="posts__info">
                  <span class="posts__author">{post.data.author}</span>
                  <span>•</span>
                  <span class="posts__date">
                    {new Date(post.data.date).toLocaleDateString("ru-RU")}
                  </span>
                </div>
                <h2 class="h3 posts__title">{post.data.title}</h2>
              </a>
            </li>
          );
        })
      }
    </ul>
  </div>
</Layout>

<style>
  .articles {
    margin-top: 48px;
  }

  h1 {
    font-size: 40px;
    margin-bottom: 32px;
    text-align: center;
  }

  ul {
    list-style-type: none;
    padding: 0;
  }

  a:hover {
    color: var(--blue);
  }

  .posts-grid {
    display: flex;
    flex-direction: column;
    gap: 64px 24px;
    max-width: 720px;
    margin: 0 auto;
  }

  .posts__title {
    font-weight: 700;
  }

  .posts__info {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .posts__cover {
    width: 100%;
    aspect-ratio: 2/1;
    min-height: 152px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    object-position: top;
    transition: transform 0.3s var(--smooth-ease);
  }

  @media screen and (any-hover: hover) {
    li:hover .posts__cover {
      transform: scale(1.02);
    }
  }

  @media screen and (max-width: 688px) {
    .posts__cover {
      height: auto;
      aspect-ratio: 2/1;
    }
  }
</style>
