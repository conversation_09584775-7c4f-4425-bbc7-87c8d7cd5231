{"about": {"investors_cite": "Digitalization is inevitable for many industries, and sports is no exception", "investors_cite_title": "Investment director at Brayne", "investors_title": "Investors", "media_title": "Media", "partners_title": "Glad to partner with", "subtitle": "15 individual tests scientifically proven to impact soccer performance", "subtitle_2": "Started in 2020 by a cross-cultural team of second-time entrepreneurs and IT engineers", "text": "We combined our cutting-edge AI and computer vision technology with our passion for sports to create the first digital hub for players, coaches, scouts and clubs worldwide. Using mobile phone footage, our algorithms can identify accurately every movement and provide advanced analysis on a variety of drills. The data is available in our apps JuniStat & JuniCoach allowing our users to track their performances and also giving players visibility in our international talent base.{br}{br}Based on this sports technology, we’re building a TID (talent identification system) where young players have digital profiles and ratings according to their achievements. This system collects and provides objective statistical data on footballers globally and significantly increases players’ chances to get scouted online, without any barriers, but only due to their talent, persistence, and motivation.", "title": "JuniStat{copyright} is the smart mobile technology that helps clubs and academies identify and develop talents in football (soccer)"}, "clubs": {"app_coach": "Coach app", "connect_club": "Connect club", "create_club": "Create club's account", "exercises": {"title": "14 tests for technical and physical player attributes that are scientifically proved to impact football (soccer) performance"}, "exercises_subtitle": "All of the exercises are integrated into the JuniCoach app and available to coaches, scouts and players. Each test represents accurately the player’s metrics in comparison with the average data from his age group", "exercises_title": "More than 15 digital exercises", "features": {"card": {"description": "Branded card with a club logo.", "title": "Interactive digital profile with actual data, graphs and game highlights."}, "data": {"card_title": "About player", "description_text": "Soccer player with a passion for the game and a drive to succeed. <PERSON> is a striker with lightning-fast footwork, excellent ball control, and a keen eye for goal. He has a natural talent for reading the game and making split-second decisions that often result in scoring opportunities for his team.", "height": "Height, cm", "position": "Position", "position_value": "CM, LCM", "title": "Additional data", "weight": "Weight, kg"}, "results": {"title": "Test results"}, "title": "After testing each player gets", "video": {"title": "Uploaded game highlights"}}, "features_card_text1": "Every player gets a digital card and a branded profile", "features_card_text2": "All player data is protected and stored in accordance with GDPR, ISO and industrial country standards. Each player is linked to his adult representative", "features_card_text3": "The club decides whether to make it’s player profiles closed or open to other users", "guide_title": "Juni platform step-by-step user manual", "hero": {"points": [{"they": "Lasers and alternatives", "we": "Easy-to-use and low-cost"}, {"they": "Subjective observations", "we": "Objective AI/ML statistics"}, {"they": "Traditional analysis", "we": "65+ parameters of physical <br />and technical performance"}], "request_presentation": "Request presentation", "reg_button": "Register", "reg_description": "Get 15 tests for a free trial!", "subtitle": "Smart testing system helps academies and clubs to collect 65+ data points for talent ID and insights into youths' development.", "title": "Test, track, and benchmark players’ skills with the JuniCoach app", "form": {"name_input": "Full name", "email_input": "Email", "phone_input": "Phone", "academy_input": "Academy Name", "send_button": "Request Presentation", "legal_text": "By clicking the button, you agree to the ", "legal_text_privacy": "privacy policy", "form_error": "We're sorry! It looks like your submission didn't go through. Please try resubmitting later. If you continue to have issues, let us <NAME_EMAIL>.", "form_success": "Thank you! We will prepare the presentation for you shortly and send it to the specified email address."}}, "how": {"equip": "Minimum equipment for accurate data collection", "equip_item": {"cones": "<PERSON><PERSON>", "phone": "Smartphone", "tape": "Measure tape", "tripod": "<PERSON><PERSON>"}, "free_trial": "Free trial access", "point1": {"title": "Coach shoots the videos following the in-app guidelines"}, "point2": {"title": "AI-powered program processes them immediately and delivers precise players' metrics"}, "title": "How it works"}, "player_example_title": "Example of player's personal card", "player_open_profile": "Open profile", "profile_protect_text": "All player data is protected and stored in accordance with GDPR, ISO and industrial country standards. Each player is linked to his adult representative", "quote": "\"Systematic testing significantly increases the level of learning of young players and contributes to the development of basic football skills\"", "recommends": {"ian_text": "“The technology measures technical and physical player attributes that are scientifically tested to impact football (soccer) performance. Academy can now quickly test players using a smartphone and tripod, upload this data soon to a digital hub and drill deeper into an individual player’s performance levels. <br /> Data is now driving the entire talent ID and development process, and the Academy can access data quickly to make any required changes to a player’s individual training plan. <br /> When evaluating players for opportunities in Europe, the Academies strive to measure potential versus current performance. Hence, it is essential to track the rate at which young players develop over time and how consistently they improve.”", "show_less": "Show less", "show_more": "Show more"}, "recommends_title": "Referrals from federations and clubs", "rendered_subtitle": "Allows clubs to quickly and efficiently find talented players and conduct digital scouting", "rendered_title": "Smart testing system", "step_text1": "Register your club and receive your access after approval", "step_text2": "Fill in the details of your team and staff in your personal club account", "step_text3": "Get in touch with us to schedule a trial and a demo for your staff", "steps": {"text1": "Monitor and benchmark players' data with peers around the globe", "text2": "Analyze data with your tools or use our insights", "text3": "Scout and select players for the offline trials", "title": "Register and explore platform features in your personal web dashboard"}, "title": "JuniCoach{copyright} helps to quickly test football players in conjunction with regular training", "trial_testing": "Free trial access"}, "whitelabel": {"page_title": "Whitelabel solutions for AI football player testing", "hero": {"badge": "100+ clubs and academies", "title": "Whitelabel solutions for AI football player testing", "description": "Your own branded section with a battery of tests in the JuniCoach app, with your club's logo and colors when ordering from 10,000 tests"}, "features": {"access": "Access for coaches and scouts to results in personal account and mobile app", "pricing": "Tests at special prices for contracts from 2 million ₽", "custom_tests": "Possibility to develop tests according to club requirements"}, "technology": {"title": "Advanced testing and scouting technology in club colors", "description": "Using only mobile phones and tripods, coaches can quickly test and get accurate player data, compare them with standards, identify and develop talents", "recommendation": "Verified by football federations and leading academies. Used for comprehensive player evaluation", "recommendation_link": "Recommendation letter ↗"}, "equipment": {"title": "Testing without expensive equipment, anywhere", "process1": "Coach shoots videos following the in-app instructions", "process2": "Platform instantly processes data and provides accurate player metrics"}, "tests": {"title": "Testing without expensive equipment, anywhere"}, "modern": {"title": "Modern approach to player selection and development", "list1": "Compare players with peers around the world", "list2": "Share profiles with parents", "list3": "Confirm coaches' decisions with objective data", "security": "Data security and safety under strict control"}, "media": {"title": "Media about us", "article1": "Freedom QJ League conducted digital player testing", "article2": "Digitalization program for Zenit-Championika students", "article3": "Player selection for festival using smart testing"}, "cta": {"title": "Connect your brand in JuniCoach", "contact_manager": "Contact manager", "description": "We will answer your questions and offer integration options", "instructions": "System operation instructions"}, "modal": {"title": "Contact manager", "close": "Close modal window", "name_label": "Name *", "name_placeholder": "Enter your name", "email_label": "Email *", "email_placeholder": "<EMAIL>", "phone_label": "Phone *", "phone_placeholder": "+1 (999) 123-45-67", "academy_label": "Academy/Club name *", "academy_placeholder": "Your academy or club name", "submit": "Submit request", "submitting": "Submitting...", "legal": "By clicking the button, you agree to the", "privacy_link": "privacy policy", "success_title": "Request sent!", "success_message": "We will contact you shortly", "error_title": "Submission error", "error_message": "Please try again or contact us directly", "retry": "Try again"}, "footer": "JuniStat LLC © Delaware, USA"}, "faq": {"feedback": "Was this answer helpful?"}, "faq_list": [{"answer": "Usually, the test results are processed with one day, but at times it may take up to five days.<br />Please, follow the instructions when taking the test to reduce the chance of rejection.", "title": "How long does it take to process a test and when will I get the results?"}, {"answer": "There are several ways:<br /><ul><li>Send an invite to join a representative in the player app</li><li>Register as a representative - <a href=\"https://app.junistat.com/sign-up?role=mentor\">app.junistat.com/sign-up?role=mentor</a> and send an invitation to the player</li><li>If the player is registered by his club, request to the club to add you as a representative of the player</li></ul><div class=\"faq__gallery\"><img src=\"/img/faq/en/rep-1-en.webp\"/><img src=\"/img/faq/en/rep-2-en.webp\"/><img src=\"/img/faq/en/rep-3-en.webp\"/><img src=\"/img/faq/en/rep-4-en.webp\"/><img src=\"/img/faq/en/rep-5-en.webp\"/></div>", "title": "How to connect a representative to a player?"}, {"answer": "You can pay for your subscription in your representative's account.<br /><ol><li>Login to the representative's account <a href=\"https://app.junistat.com/login?role=mentor\" >app.junistat.com/login?role=mentor</a></li><li>Select the player you want to subscribe to</li><li>Click \"Subscribe\"</li></ol><div class=\"faq__gallery\"><img src=\"/img/faq/en/pay-en-1.webp\"/><img src=\"/img/faq/en/pay-en-2.webp\"/><img src=\"/img/faq/en/pay-en-3.webp\"/></div>", "title": "How to pay for your subscription?"}, {"answer": "You can follow the player's results in your representative account - <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role= mentor</a> or in the player database overall rating in case his profile is open — <a href=\"https://junistat.com/\">junistat.com</a>", "title": "How to track your child's results?"}, {"answer": "<ul><li>The test results become less relevant over time, test regularly to maintain your rating as high as possible</li> <li>Average test scores are improving. New users are performing well which affects the overall ranking</li> <li>Your player scores have declined</li></ul>", "title": "Why does the player rating change?"}, {"answer": "<ol><li>Log into your representative’s account <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor</a></li><li>Select the player for which you want to add a video or description.</li><li>Select the \"About\" tab in the menu</li><li>Add a description or upload a video</li><li>Click \"Save\"</li></ol><div class=\"faq__gallery is--1col\"><video controls><source src='/img/faq/en/description-en-1.mp4' type='video/mp4'></video></div>", "title": "How to add video and description to a player’s profile?"}, {"answer": "<ol><li>Login to representative's account <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor</a></li><li>Click the \"Merge Profiles\" button</li><li>Select player profiles from the academy and the JuniStat app</li></ol>", "title": "How to merge two identical player profiles into one?"}, {"answer": "The app subscription gives you access to all tests and also the option to make a player profile visible to other users around the world.", "title": "What does the subscription include?"}, {"answer": "Register in the JuniStat app, test yourself and make your profile visible to clubs and scouts worldwide.", "title": "How do I get connected to clubs?"}, {"answer": "<ol><li>Log into your representative’s account <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor</a></li><li>Log into your representative’s account</li><li>Go to the “Settings” section on his profile</li><li>Scroll down and select “Unlink player”</li></ol><div class=\"faq__gallery is--1col\"><video controls><source src='/img/faq/en/unlink-en-1.mp4' type='video/mp4'></video></div>", "title": "How to unlink a representative from a player?"}, {"answer": "Email us <a href=\"mailto:<EMAIL>\"><EMAIL></a>. Enter the player's name, phone number or email.", "title": "How to delete an account?"}, {"answer": "The player does not need to have a representative to do this. Ask the current representative to unlink the player from their profile.", "sub_answers": [{"answer": "<h4>Email the representative</h4><ol><li>Login to the academy account <a href=\"https://app.junistat.com/login?role=academy\">app.junistat.com/login?role=academy</a></li><li>Click “Invite a representative” on the list of players</li><li>Enter the representative’s email and click “Submit request”</li></ol><br /><h4>Share Profile</h4><ol><li>Login to the academy account <a href=\"https://app.junistat.com/login?role=academy\"> app.junistat.com/login?role=academy</a></li><li>Click \"Share\" on the list of players</li><li>Share the link with the representative</li> <li>The representative must register using this link</li></ol>", "title": "In the academy account"}, {"answer": "<ol><li>Open the player's list</li> <li>On the player profile, click “Invite representative”</li> <li>Fill the email of the representative and click “Send”</li></ol>", "title": "In the JuniCoach app"}], "title": "How to send an invitation to a representative?", "type": "academy"}, {"answer": "Contact the current representative to unlink the player from their profile. When the player has no representative attached to his profile, you can invite a new one.", "title": "How to replace player representative?", "type": "academy"}], "meta": {"description": "Smart apps for young football players, coaches and clubs. International rating, testing, talents identification, and scouting"}, "navigation": {"about": "About", "clubs": "Clubs, coaches, scouts", "faq": "FAQ", "parents": "Parents and Representatives", "press_kit": "Press kit", "privacy": "Privacy policy", "rating": "Rating", "rating_system": "Rating system", "scouting": "Digital scouting", "sponsors": "Sponsors", "team": "Team"}, "parents": {"about_cards": "Personal card, digital profile, and rating in our international talent base", "about_skills_subtitle": "An opportunity to compete and benchmark with players all around the world", "about_skills_title": "Objective skills assessment and recommendations for further development", "app_for_player": "Player app", "chart_text": "Every player’s technical and physical data is collected in his digital profile with his bio and highlights. Depending on his rating and performance, every player is eligible for a club invitation", "create_parent_profile": "Create a representative profile", "title": "JuniStat{copyright} identifies young football talents"}, "pricing": {"from_3000": "from 3,000 players", "from_3000_price_month": "{currency}4/month", "from_3000_price_year": "{currency}40/year", "license": {"point1": "Unlimited access to tests in JuniStat and JuniCoach mobile apps", "point2": "Digital cards and player profiles with an objective evaluation skills", "point3": "Dynamics of development, players' benchmarking, and record of progress (digital track", "point4": "Player ratings in the international database of young football players", "point5": "Development tips, insights, and recommendations for players and coaches", "point6": "A personal web account of the Academy with statistics, analytics, videos, selection block, and reports", "title": "License includes"}, "price_player": "Price per player", "price_player_description": "1 player = 1 license", "title": "Plans for Business", "up_to_1000": "up to 1 000 players", "up_to_1000_price_month": "{currency}5/month", "up_to_1000_price_year": "{currency}50/year", "up_to_3000": "from 1,000 to 3,000 players", "up_to_3000_price_month": "{currency}4.5/month", "up_to_3000_price_year": "{currency}45/year"}, "rating": {"banner": {"subtitle": "Begin testing by downloading the app", "title": "Get your rating"}, "card_clubs": "Solution for clubs", "card_parents": "Parents and Representatives", "example": {"point1": "Personal card with unbiased skills assessment", "point2": "Digital profiling with objective performance data, bio and highlights", "point3": "Rating in our international talents base", "point4": "Chance to be scouted online", "title": "Each player gets"}, "filters": "Filters", "grid": {"error": "Error loading players. Refresh the page or try again later", "next": "Next", "prev": "Prev", "empty": "Players not found or hidden"}, "popup": {"button_login": "Log In", "button_signUp": "Sign Up", "description": "To get full access to the player database and advanced search", "title": "Create your account"}, "search_placeholder": "Player Name", "select_club": "Select a club", "titleAllPlayers": "All players", "title_players_month": "Players of the Month", "subtitle_players_month": "Complete the tests and get into the TOP football players of the month. Both results and activity are taken into account", "scout": {"title": "Are you a scout, agent, or representative of a football academy?", "text": "Receive monthly analytical reports and insights on the top players who have passed our tests. Discover future football stars with JuniStat", "button": "Subscribe", "terms": "By submitting this form, you agree to the <a href='https://junistat.com/privacy/' target='_blank'>Privacy Policy</a>", "form-success": "Thanks for your subscription. You will receive our next email with the top players of the month", "form-error": "We're sorry! It looks like your submission didn't go through. Please try resubmitting later. If you continue to have issues, let us <NAME_EMAIL>"}}, "sign_up": "Sign Up", "Top 5 Startups 2025": "Top 5 Startups 2025", "sponsors": {"contact": "Feel free to contact — <PERSON><PERSON><PERSON>", "text": "Currently, there are over 100 000 young players using the service worldwide. New talent is joining the platform every day, however, not all of them can afford the subscription. Sponsors can help those players pay for their subscription, while packages start from 100 users.", "title": "Help young players get a chance!"}, "team": {"ambassador_title": "Ambassadors and Sales", "how_become_ambassador_text": "If you would like to represent JuniStat in your country, please contact us —", "how_become_ambassador_title": "How to become our Ambassador?", "team_title": "Management and operations"}, "delete_account": {"title": "Account deletion request", "description": "Your account and all associated information will be deleted within 48 hours", "button": "Delete account", "error": "We're sorry! It looks like your submission didn't go through. Please try resubmitting later. If you continue to have issues, let us <NAME_EMAIL>", "success": "Your request has been received. The account and all associated information will be deleted"}, "help_center": {"back_button": "← All instructions", "title": "Help center"}, "blog": {"title": "Blog"}}