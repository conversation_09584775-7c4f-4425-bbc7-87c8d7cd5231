/*#region [Font]*/
h1,
.h1 {
  font-weight: 800;
  font-size: clamp(1.5rem, 0.6667rem + 4.1667vw, 4rem);
}

@media screen and (max-width: 688px) {
  h1,
  .h1 {
    font-size: 8vw;
  }
}

h2,
.h2 {
  font-weight: 800;
  font-size: 40px;
}

@media screen and (max-width: 688px) {
  h2,
  .h2 {
    font-size: 21px;
    font-weight: 700;
  }
}

h3,
.h3 {
  font-weight: 600;
  font-size: 24px;
}

@media screen and (max-width: 688px) {
  h3,
  .h3 {
    font-size: 20px;
  }
}

p,
.p {
  font-size: 18px;
}

body {
  font-size: 18px;
}
/*#endregion [Font]*/

.hero {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 55px;
  margin-bottom: 88px;
}

@media screen and (max-width: 992px) {
  .hero {
    margin-bottom: 48px;
  }
}

.hero__headline {
  margin-bottom: 24px;
}

.hero__description-wrap {
  margin-bottom: 24px;
}

.hero__desription-text {
  font-size: clamp(1rem, 0.6667rem + 1.6667vw, 2rem);
}

.hero__preview {
  border-radius: 16px;
  overflow: hidden;
}

@media screen and (max-width: 992px) {
  .hero__preview {
    height: 320px;
    object-fit: cover;
    object-position: 15% 0;
  }
}

#section-features {
  margin-top: 88px;
}

.features__grid {
  margin-top: 48px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

@media screen and (max-width: 992px) {
  .features__grid {
    display: flex;
    flex-direction: column;
  }
}

.features__grid-card {
  background-color: var(--white);
  border-radius: 8px;
  padding: 16px;
  position: relative;
  min-height: 150px;
  overflow: hidden;
}

@media screen and (max-width: 992px) {
  .features__grid-card {
    display: flex;
    flex-direction: column-reverse;
  }
}

.features__grid-card.is--dashboard {
  grid-row: 1 / 2 span;
  grid-column: 2 / 2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}

.features__grid-card.is--dashboard > .features__card-info {
  max-width: 100%;
}

.features__card-info {
  max-width: 65%;
}

@media screen and (max-width: 992px) {
  .features__card-info {
    max-width: unset;
  }
}

.features__card-info > * {
  margin-bottom: 8px;
}

.features__card-image {
  max-width: 180px;
  position: absolute;
  top: 0;
  right: 0;
}

@media screen and (max-width: 992px) {
  .features__card-image {
    position: static;
    margin-bottom: 24px;
  }
}

/*#region [Features images]*/
.features__card-image.is--record {
  max-width: 200px;
  top: -34%;
}

@media screen and (max-width: 992px) {
  .features__card-image.is--record {
    margin-top: -12%;
  }
}

.features__card-image.is--dashboard {
  max-width: 95%;
  position: relative;
  top: -10%;
}

.features__card-image.is--stats {
  max-width: 250px;
  top: -10%;
}

.features__card-image.is--heatmap {
  border-radius: 8px;
  max-width: 200px;
  top: 5%;
  right: 1%;
}

.features__card-image.is--stream {
  max-width: 150px;
  top: 5%;
  right: 5%;
}
/*#endregion [Features images]*/

/*#region [Points]*/
#section-points {
  margin-top: 88px;
}

.container.is--points {
  max-width: 890px;
}

.points > * + * {
  margin-top: 24px;
}

.points__card {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: var(--white);
  border-radius: 8px;
  padding: 16px 30px;
}

@media screen and (max-width: 992px) {
  .points__card {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
  }

  .points__card > p {
    font-size: 16px;
  }
}

.points__col {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: 50%;
}

@media screen and (max-width: 992px) {
  .points__col {
    margin-bottom: 16px;
  }
}

.points__icon {
  --icon-size: 72px;

  width: var(--icon-size);
  height: var(--icon-size);
}

.points__card-headline {
  margin-left: 24px;
}

@media screen and (min-width: 992px) {
  .points__card-description {
    margin-left: auto;
  }
}
/*#endregion [Points]*/

/*#region [Footer]*/
#section-footer {
  margin-top: 88px;
}

.footer {
  text-align: center;
  margin-bottom: 32px;
}
/*#endregion [Footer]*/
