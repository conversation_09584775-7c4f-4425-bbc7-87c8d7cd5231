//#region [Metrika]
(function () {
  "use strict";

  // Флаг, что Метрика уже загрузилась.
  let loadedMetrika = false,
    // Ваш идентификатор сайта в Яндекс.Метрика.
    metricaId = 97722806,
    // Переменная для хранения таймера.
    timerId;

  // Для бота Яндекса грузим Метрику сразу без "отложки",
  // чтобы в панели Метрики были зелёные кружочки
  // при проверке корректности установки счётчика.
  if (navigator.userAgent.indexOf("YandexMetrika") > -1) {
    loadMetrika();
  } else {
    // Подключаем Метрику, если юзер начал скроллить.
    window.addEventListener("scroll", loadMetrika, {
      passive: true,
    });

    // Подключаем Метрику, если юзер коснулся экрана.
    window.addEventListener("touchstart", loadMetrika);

    // Подключаем Метрику, если юзер дернул мышкой.
    document.addEventListener("mouseenter", loadMetrika);

    // Подключаем Метрику, если юзер кликнул мышкой.
    document.addEventListener("click", loadMetrika);

    // Подключаем Метрику при полной загрузке DOM дерева,
    // с "отложкой" в 1 секунду через setTimeout,
    // если пользователь ничего вообще не делал (фоллбэк).
    document.addEventListener("DOMContentLoaded", loadFallback);
  }

  function loadFallback() {
    timerId = setTimeout(loadMetrika, 3500);
  }

  function loadMetrika(e) {
    // Пишем отладку в консоль браузера.
    if (e && e.type) {
      //console.log(e.type);
    } else {
      //console.log('DOMContentLoaded');
    }

    // Если флаг загрузки Метрики отмечен,
    // то ничего более не делаем.
    if (loadedMetrika) {
      return;
    }

    (function (m, e, t, r, i, k, a) {
      m[i] =
        m[i] ||
        function () {
          (m[i].a = m[i].a || []).push(arguments);
        };
      m[i].l = 1 * new Date();
      for (var j = 0; j < document.scripts.length; j++) {
        if (document.scripts[j].src === r) {
          return;
        }
      }
      (k = e.createElement(t)),
        (a = e.getElementsByTagName(t)[0]),
        (k.async = 1),
        (k.src = r),
        a.parentNode.insertBefore(k, a);
    })(window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");

    ym(metricaId, "init", {
      clickmap: true,
      trackLinks: true,
      accurateTrackBounce: true,
      webvisor: true,
    });

    // Отмечаем флаг, что Метрика загрузилась,
    // чтобы не загружать её повторно при других
    // событиях пользователя и старте фоллбэка.
    loadedMetrika = true;

    // Очищаем таймер, чтобы избежать лишних утечек памяти.
    clearTimeout(timerId);

    // Отключаем всех наших слушателей от всех событий,
    // чтобы избежать утечек памяти.
    window.removeEventListener("scroll", loadMetrika);
    window.removeEventListener("touchstart", loadMetrika);
    document.removeEventListener("mouseenter", loadMetrika);
    document.removeEventListener("click", loadMetrika);
    document.removeEventListener("DOMContentLoaded", loadFallback);
  }
})();
//#endregion [Metrika]
