---
import "@styles/main.scss";
//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import AppButton from "@components/AppButton.astro";
import RocketLaunch from "@components/icons/RocketLaunch.astro";

//#endregion [Components]

//#region [Styles]

//#endregion [Styles]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import EgorImg from "@assets/images/talents/egor.png";
import PopovImg from "@assets/images/talents/popov.png";
import ProninImg from "@assets/images/talents/pronin.png";

const TestIcons = await Astro.glob(
  "/src/assets/images/talents/tests/*.{png,svg,jpg}",
).then((files) => {
  return files.map((file) => {
    return file.default;
  });
});

import VideoPosterImg from "@assets/images/talents/video-poster.jpg";
const VideoPosterImgOptimized = await getImage({
  src: VideoPosterImg,
  format: "webp",
});
//#endregion [Images]

//#region [Videos]
import talentVideoMp4 from "@assets/videos/talents/talent-web.mp4";
import talentVideoWebm from "@assets/videos/talents/talent-web.webm";
//#endregion [Videos]
---

<Layout title="Просмотры России с Юнистат" contentOnly>
  <section class="section">
    <div class="container">
      <div class="hero">
        <h1 class="hero__headline">
          Просмотры и отбор юных футболистов в Российские академии с помощью
          онлайн-тестирований
        </h1>
        <p class="hero__text h3">
          Умная система тестов JuniStat<sup>®</sup> — проверена специалистами РФС и применяется
          для комплексной оценки игроков
        </p>
      </div>
    </div>
  </section>
  <section class="section section-space">
    <div class="container">
      <main class="main">
        <div class="main__col guide">
          <h2>
            Пошаговое руководство по созданию профиля и прохождению тестов
          </h2>
          <div class="card guide__card">
            <div class="guide__circle-number h2">1</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Регистрация</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>Нажмите ссылку и зарегистрируйтесь</p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Следуйте инструкциям на электронной почте чтобы установить
                    мобильное приложение JuniStat и настроить веб-кабинет
                    родителя
                  </p>
                </li>
              </ul>
              <AppButton
                as="a"
                style="mod2"
                class="reg-button"
                skew
                href="https://app.junistat.com/invite-mentors?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2FkZW15SWQiOiIwN2E2Njk4MC1iN2UxLTQ4YzUtYmEyNi0wMzQxZWJmODNjZWMiLCJpYXQiOjE3MjEzMjY5NDgsImV4cCI6MTcyOTEwMjk0OH0.ID7dC6LnLREFn2UmpgjiwDoMcSVaLY4NDTwLWMiDnvQ"
                target="_blank"
              >
                <RocketLaunch />
                <span class="h3" style="font-weight: 700">Пройти отбор</span>
              </AppButton>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">2</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Оплата подписки</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Оплатите подписку в кабинете родителя и получите полный
                    доступ к тестам в мобильном приложении JuniStat
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Теперь, cкауты и селекционеры смогут следить за профилем
                    игрока и результатами тестов
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">3</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">
                Усиление профиля и набор рейтинга
              </h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Выполняйте тесты и набирайте рейтинг. Загружайте видео
                    с игр. Опишите особенности игрока
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>Эта информация поможет нам в принятии решения об отборе</p>
                </li>
              </ul>
              <div class="guide__profile-examples">
                <h3 class="guide__card-headline h4">
                  Примеры профилей игроков
                </h3>
                <p>
                  Ваш игрок автоматически сравнивается с тысячами сверстников
                  по всему миру и получает рейтинг, соответствующий его навыкам
                </p>
                <div class="guide__profile-cards">
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/874ae8c9-7912-4d15-a32e-3c72b2f11aa6/training"
                    target="_blank"
                  >
                    <Image
                      src={EgorImg}
                      alt="Пример карточки игрока"
                      width={254}
                    />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/bd3557a0-ab7f-4cdf-a450-4768b4241d1d/training"
                    target="_blank"
                  >
                    <Image
                      src={PopovImg}
                      alt="Пример карточки игрока"
                      width={254}
                    />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/a4d846dc-9737-4fa5-bb65-6eb92ee8b514/training"
                    target="_blank"
                  >
                    <Image
                      src={ProninImg}
                      alt="Пример карточки игрока"
                      width={254}
                    />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main__col info">
          <div class="card info">
            <h2>
              Покажи высокие результаты в тестах и получи приглашение
              на просмотр!<br /><br />Предпочтение отдаётся игрокам 2009, 2010,
              2011, 2012 гг. Успейте пройти тесты до 1 декабря!
            </h2>

            <div class="info__video video">
              <video
                class="info__video-tag"
                poster={VideoPosterImgOptimized.src}
                style="width: 100%"
                controls
                playsinline
              >
                <source src={talentVideoWebm} type="video/webm" />
                <source src={talentVideoMp4} type="video/mp4" />
              </video>
            </div>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <h2>Выполните следующие тесты из мобильного приложения!</h2>
              <p>
                Профиль игрока с результатами тестов будет автоматически показан
                академиям и скаутам. Мы свяжемся с вами, чтобы дать рекомендации
                или позвать игрока на просмотр.
              </p>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">Обязательные тесты</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image src={TestIcons[2]} alt="Test icon" />
                  <p>Прыжок вверх</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[1]} alt="Test icon" />
                  <p>Бег 15м со старта</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[4]} alt="Test icon" />
                  <p>Змейка</p>
                </div>
              </div>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">По желанию</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image src={TestIcons[5]} alt="Test icon" />
                  <p>Удар левой/правой</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[0]} alt="Test icon" />
                  <p>Стрела без мяча</p>
                </div>
                <div class="info__test">
                  <Image src={TestIcons[3]} alt="Test icon" />
                  <p>Другие</p>
                </div>
              </div>
            </div>
            <AppButton
              as="a"
              style="mod2"
              skew
              class="reg-button"
              href="https://app.junistat.com/invite-mentors?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2FkZW15SWQiOiIwN2E2Njk4MC1iN2UxLTQ4YzUtYmEyNi0wMzQxZWJmODNjZWMiLCJpYXQiOjE3MjEzMjY5NDgsImV4cCI6MTcyOTEwMjk0OH0.ID7dC6LnLREFn2UmpgjiwDoMcSVaLY4NDTwLWMiDnvQ"
              target="_blank"
            >
              <RocketLaunch />
              <span class="h3" style="font-weight: 700">Пройти отбор</span>
            </AppButton>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <p>
                Мы готовы ответить на ваши вопросы. Пожалуйста, свяжитесь с нами
                в чатах или по электронной почте <a
                  href="mailto:<EMAIL>"
                  target="_blank"><EMAIL></a
                >
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </section>
</Layout>

<style>
  @import "@styles/pages/talent.scss";
</style>

<script>
  import { track } from "@amplitude/analytics-browser";

  const regButtons = document.querySelectorAll(
    ".reg-button",
  ) as NodeListOf<HTMLElement>;

  regButtons.forEach((button) => {
    button.addEventListener("click", () => {
      track("Click registration button");
    });
  });
</script>
