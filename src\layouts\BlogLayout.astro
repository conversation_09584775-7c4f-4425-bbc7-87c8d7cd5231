---
import "@styles/main.scss";

//#region [i18n]
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]

import Layout from "@layouts/Layout.astro";

import { Image } from "astro:assets";

const { frontmatter } = Astro.props;

const astroUrl = Astro.site?.origin + Astro.url.pathname;

// function convertToISODate(dateStr: string) {
//   if (!dateStr) return;
//   // Split the date string into parts
//   const parts = dateStr.split("/");

//   // Extract day, month, and year
//   const day = parts[0];
//   const month = parts[1];
//   const year = parts[2];

//   // Construct the ISO 8601 formatted date
//   const isoDate = `${year}-${month}-${day}`;

//   return isoDate;
// }

const isoDate = frontmatter.date;
// convertToISODate(frontmatter.date);

let schema =
  !isoDate || !frontmatter.author
    ? JSON.stringify({})
    : JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Article",
        headline: frontmatter.title,
        datePublished: isoDate,
        author: {
          "@type": "Organization",
          name: "JuniStat",
        },
        publisher: {
          "@type": "Organization",
          name: "JuniStat",
        },
        mainEntityOfPage: {
          "@type": "WebPage",
          "@id": astroUrl,
        },
      });

const localDate = new Date(isoDate).toLocaleDateString("ru-RU");
---

<Layout title={frontmatter.title}>
  <section class="section-space">
    <div class="container blog-header">
      <div class="blog-header__texts">
        {
          frontmatter.author && localDate && (
            <div>
              {frontmatter.author}, {localDate}{" "}
            </div>
          )
        }
        <h1>{frontmatter.title}</h1>

        <slot name="header" />
      </div>
      <div class="blog-header__image">
        <Image
          src={frontmatter.cover}
          alt={frontmatter.title || ""}
          loading="eager"
        />
      </div>
    </div>
    <article class="container is--blog">
      <slot />
    </article>

    <section id="footer-cards" class="section-space">
      <div class="container">
        <div class="footer-cards">
          <a
            class="footer-cards__card"
            id="go-to-parents-card"
            href={translatePath("/parents")}
          >
            <!-- prettier-ignore -->
            <svg class="footer-cards__card-icon" xmlns="http://www.w3.org/2000/svg" width="72" height="72" fill="none" viewBox="0 0 72 72"><path fill="#000" d="M28.806 11.1c-3.462 5.811-1.905 15.681 4.272 27.075 2.79 5.136 2.091 9.06 1.014 11.445-2.946 6.534-11.025 8.397-19.575 10.368C8.625 61.35 9 62.598 9 72H3.015L3 68.277c0-7.56.597-11.925 9.534-13.989 10.095-2.331 20.064-4.419 15.27-13.254C13.605 14.847 23.754 0 39 0c9.963 0 17.91 6.351 17.91 18.501C56.91 29.166 51.063 39 49.761 42h-6.345c1.176-4.608 7.497-13.098 7.497-23.526 0-15.459-17.601-14.955-22.107-7.374zM69 57h-9v-9h-6v9h-9v6h9v9h6v-9h9v-6z"/>
            </svg>
            <h3 class="footer-cards__card-title">
              {t("rating.card_parents")}
            </h3>
          </a>

          <a
            class="footer-cards__card"
            id="go-to-clubs-card"
            href={translatePath("/clubs")}
          >
            <!-- prettier-ignore -->
            <svg class="footer-cards__card-icon" xmlns="http://www.w3.org/2000/svg" width="72" height="72" fill="none" viewBox="0 0 72 72"><path fill="#000" fill-rule="evenodd" d="M21 6c5.085 5.826 7.113 9 12 9h39v51H0V6h21zm18 33v-9h-6v9h-9v6h9v9h6v-9h9v-6h-9z" clip-rule="evenodd"/>
            </svg>
            <h3 class="footer-cards__card-title">
              {t("rating.card_clubs")}
            </h3>
          </a>
        </div>
      </div>
    </section>
  </section>
</Layout>

<script type="application/ld+json" set:html={schema} />

<style lang="scss">
  @use "sass:color";
  @use "@styles/_variables.scss" as *;
  @use "@styles/_screens.scss" as *;

  .container.is--blog {
    margin-top: $space-128;
  }

  .blog-header {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    gap: $space-32;

    > * {
      flex: 1 1 40%;
    }
  }

  @media screen and (max-width: 688px) {
    .blog-header {
      flex-direction: column;
    }
  }

  .blog-header__texts {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: $space-16;

    @media screen and (min-width: $tablet) {
      h1 {
        font-size: 58px;
      }
    }

    @media screen and (max-width: $tablet) {
      h1 {
        font-size: 32px;
      }
    }
  }

  .blog-header__image {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 500px;
    border-radius: 16px;
    overflow: hidden;
    max-height: 600px;
    max-width: 100%;
  }

  .blog-header__image > img {
    border-radius: 16px;
    overflow: hidden;
  }

  /*#region [Footer cards]*/

  .footer-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $space-16;

    @media screen and (max-width: $mobile) {
      display: flex;
      flex-direction: column;
    }
  }

  .footer-cards__card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: $space-24;
    text-align: center;
    gap: $space-24;

    background-color: $white;
    border-radius: $space-8;
    border: 1px solid $gray;

    @include transition-colors;

    &:hover {
      background-color: color.change($white, $lightness: 97%);
    }

    &.is--material {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      text-align: left;

      @media screen and (max-width: $mobile) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    &.is--accent {
      background-color: $primary;
    }
  }

  .footer-cards__card-side {
    width: 80%;
    display: flex;
    flex-direction: column;
    gap: $space-8;

    @media screen and (max-width: $mobile) {
      width: 100%;
    }
  }

  .footer-cards__card-text {
    width: 80%;

    @media screen and (max-width: $mobile) {
      width: 100%;
    }
  }

  .footer-cards__card-icon {
    width: $space-64;
    height: $space-64;
  }

  .download-buttons.is--footer {
    justify-content: center;
    align-items: center;
    margin-top: 0;
  }
  /*#endregion [Footer cards]*/
</style>

<style is:global lang="scss">
  .container.is--blog {
    max-width: 780px;

    .block,
    .frame,
    blockquote,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    hr,
    label,
    ol,
    pre,
    table {
      margin-block-start: calc(1em + 1ex);
    }

    p,
    ul,
    li {
      margin-block-start: calc(0.5em + 1ex);
    }

    img {
      max-width: 100%;
      border-radius: 8px;
    }

    a[href] {
      color: var(--blue);
    }

    a[href]:hover {
      color: var(--accent);
    }
  }
</style>
