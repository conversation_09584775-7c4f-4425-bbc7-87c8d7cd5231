@use "sass:color";
@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Rating]*/
.rating {
  position: relative;
}

/*#region [Example]*/
#example {
  margin-top: $space-80;
}
/*#endregion [Example]*/

/*#region [Header]*/
.rating__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $space-48;

  @media screen and (max-width: $mobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: $space-16;
  }
}

.rating__header-filters {
  display: flex;
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: $space-8;
    width: 100%;
  }
}

.rating__filter-button {
  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.search__input-wrap {
  position: relative;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.search__input-icon {
  position: absolute;
  left: 0.5rem;
  width: $space-24;

  &--active {
    color: $blue;
  }

  &.is--relative {
    position: relative;
    left: 0;
    margin-right: $space-8;
  }
}

.search__input {
  width: 100%;
  height: $space-56;
  border-radius: $space-8;
  border: $gray 1px solid;
  padding: $space-8 $space-8 $space-8 2.2rem;
}

.rating__academy-select {
  height: $space-56;
  border-radius: $space-8;
  border: $gray 1px solid;
  padding: $space-8;
  color: $black;

  appearance: none;
  background: white;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16px' height='16px' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M6.1018 8C5.02785 8 4.45387 9.2649 5.16108 10.0731L10.6829 16.3838C11.3801 17.1806 12.6197 17.1806 13.3169 16.3838L18.8388 10.0731C19.5459 9.2649 18.972 8 17.898 8H6.1018Z' fill='%23212121'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}
/*#endregion [Header]*/

/*#endregion [Rating]*/

/*#region [CTA]*/
.cta {
  padding: $space-64 $space-128;
  color: $white;
  background-position: 0% 0%;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  border-radius: $space-16;

  @media screen and (max-width: $mobile) {
    background-position: 40% 0%;
    padding: $space-32 $space-16;
  }
}

.cta__subtitle {
  margin-top: $space-16;
  margin-bottom: $space-48;
}

.download-buttons {
  display: flex;
  gap: $space-16;
  margin-top: $space-32;

  & img {
    max-height: $space-64;
  }

  @media screen and (max-width: $mobile) {
    margin-top: $space-48;
  }
}

.download-button {
  @include transition-opacity;
  &:hover {
    opacity: 60%;
  }
}
/*#endregion [CTA]*/

/*#region [Footer cards]*/
.footer-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.footer-cards__card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: $space-24;
  text-align: center;
  gap: $space-24;

  background-color: $white;
  border-radius: $space-8;
  border: 1px solid $gray;

  @include transition-colors;

  &:hover {
    background-color: color.change($white, $lightness: 93%);
  }

  &.is--material {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    @media screen and (max-width: $mobile) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &.is--accent {
    background-color: $primary;
  }
}

.footer-cards__card-side {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: $space-8;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-text {
  width: 80%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-icon {
  width: $space-64;
  height: $space-64;
}

.download-buttons.is--footer {
  justify-content: center;
  align-items: center;
  margin-top: 0;
}
/*#endregion [Footer cards]*/

/*#region [SignUp modal]*/
.signup-modal {
  display: flex;
  flex-direction: column;
  row-gap: $space-24;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
}

.signup-modal__close-icon {
  width: $space-32;
  height: $space-32;
  cursor: pointer;
  position: absolute;
  right: $space-16;
  top: $space-16;

  @include transition-opacity;

  &:hover {
    opacity: 0.4;
  }
}

.signup-modal__icon {
  max-width: $space-128;
}

.signup-modal__title {
  @media screen and (min-width: $tablet) {
    font-size: 2rem;
  }
}

.signup-modal__description {
  margin-top: $space-16;
  @media screen and (min-width: $tablet) {
    font-size: 1.5rem;
  }
}

.signup-modal__buttons {
  width: 100%;
}

.signup-modal__buttons > * + * {
  margin-top: $space-16;
}

/*#endregion [SignUp modal]*/
