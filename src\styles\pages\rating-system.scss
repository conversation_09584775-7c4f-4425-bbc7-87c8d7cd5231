@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Hero]*/
#hero {
  margin-top: $space-48;
}

.hero {
  display: flex;
  column-gap: $space-128;
}

.hero__text {
  margin-top: $space-24;
}
/*#endregion [Hero]*/

/*#region [Raw]*/
.section__title-text {
  margin-top: $space-16;
}

.raw__exercises-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $space-48;
  margin-top: $space-64;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
    gap: $space-32;
  }
}

.raw__exercises-nested-col {
  display: flex;
  flex-direction: column;
  gap: $space-48;

  @media screen and (max-width: $mobile) {
    display: none;
  }
}
/*#endregion [Raw]*/

/*#region [Micro-skills]*/
.micro-skills__img {
  display: block;
  margin-top: $space-48;
}
/*#endregion [Micro-skills]*/

/*#region [Tests]*/
.tests {
  display: flex;
  gap: $space-48;

  @media screen and (max-width: $mobile) {
    flex-direction: column-reverse;
    gap: $space-32;
  }
}

.tests__header {
  max-width: 40%;

  @media screen and (max-width: $mobile) {
    max-width: none;
  }
}

.tests__images {
  width: 100%;
  column-count: 3;

  @media screen and (max-width: $mobile) {
    display: flex;
    overflow: scroll;
    column-count: 1;
  }
}

.tests__img {
  display: block;
  margin-bottom: $space-16;

  @media screen and (max-width: $mobile) {
    width: 280px;
    height: auto;
    flex: 1 0 280px;
  }
}

/*#endregion [Tests]*/

/*#region [Micro skills]*/
.micro-skills__img {
  @media screen and (max-width: $mobile) {
    display: none;
  }

  &.is--mobile {
    display: none;

    @media screen and (max-width: $mobile) {
      display: block;
    }
  }
}
/*#endregion [Micro skills]*/
