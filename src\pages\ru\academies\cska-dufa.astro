---
//#region [i18n]
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]

//#region [Styles]
import "@styles/main.scss";
import "@styles/pages/rating.scss";
//#endregion [Styles]

//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import PlayersGrid from "@components/players-grid/PlayersGrid.svelte";
import CardProfileExample from "@components/CardProfileExample.astro";
import GridAcademySelector from "@components/players-grid/GridAcademySelector.svelte";
import AppButton from "@components/AppButton.astro";
import DownloadButtons from "@components/DownloadButtons.astro";
import qrCode from "@assets/images/rating/qr-code.svg";
//#endregion [Components]

//#region [Images]
import Logo from "@assets/images/academies/cska-fest/cska-logo.svg";

// Tests icons
import KickLeft from "@assets/images/academies/cska-fest/tests/monster-kick-left-min.jpg";
import KickRight from "@assets/images/academies/cska-fest/tests/monster-kick-right-min.jpg";
import Jump from "@assets/images/academies/cska-fest/tests/jump-place-min.jpg";
//#endregion [Images]
---

<Layout
  contentOnly
  title="ДЮФА ПФК ЦСКА КУРКИНО"
  description="Результаты тестирований игроков"
  chat={false}
  noindex
>
  <section id="hero" class="section-space-small">
    <div class="container">
      <div class="hero">
        <Image class="hero__logo" src={Logo} alt="ЦСКА" />
        <h1>ДЮФА ПФК ЦСКА КУРКИНО</h1>
        <AppButton class="hero__button" typeStyle="secondary" as="button">
          <svg
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            ><path
              d="M12 1.999c5.524 0 10.002 4.478 10.002 10.002 0 5.523-4.478 10.001-10.002 10.001-5.524 0-10.002-4.478-10.002-10.001C1.998 6.477 6.476 1.999 12 1.999Zm-.004 8.25a1 1 0 0 0-.992.885l-.007.116.003 5.502.007.117a1 1 0 0 0 1.987-.002L13 16.75l-.003-5.501-.007-.117a1 1 0 0 0-.994-.882ZM12 6.5a1.251 1.251 0 1 0 0 2.503A1.251 1.251 0 0 0 12 6.5Z"
              fill="#212121"></path></svg
          >
          Как это работает?
        </AppButton>
      </div>
    </div>
  </section>

  <section id="rating" class="section-space-small">
    <div class="container">
      <div class="rating">
        <!-- <PlayersGrid client:only="svelte">
          <GridAcademySelector
            slot="selector"
            lang="ru"
            client:only="svelte"
            academies={[
              {
                name: "Все игроки",
                franchiseId: "4dd59bc3-b886-409b-a3bc-3c19b2b0fcf3",
                academyId: "",
              },
            ]}
          />
        </PlayersGrid> -->

        <h2 class="rating__title">Результаты тестов</h2>
        <div class="rating__tests-list">
          <a
            class="rating__test-card"
            href="https://app.junistat.com/player/a983f0d1-3cde-4711-97ed-91694aa8528f/test-results?pageIndex=1&id=2bf554c0-de44-429c-885d-f887bef9f259&testTag=monsterKickRF&from=2024-09-01T00%3A00%3A00.000Z&to=2024-09-01T20%3A59%3A59.999Z&orderDirection=ASC&orderField=f73&tags[]=f73&fromPlayerAcademy=true"
          >
            <Image
              class="rating__test-icon"
              src={KickLeft}
              alt="Удар левой ногой"
            />
            <span class="rating__test-label">Удар левой ногой</span>
          </a>
          <a
            class="rating__test-card"
            href="https://app.junistat.com/player/a983f0d1-3cde-4711-97ed-91694aa8528f/test-results?pageIndex=1&id=f96501a2-e34c-4c84-8639-40faf51afbdd&testTag=monsterKickLF&from=2024-09-01T00%3A00%3A00.000Z&to=2024-09-01T20%3A59%3A59.999Z&orderDirection=ASC&orderField=f73&tags[]=f73&fromPlayerAcademy=true"
          >
            <Image
              class="rating__test-icon"
              src={KickRight}
              alt="Удар правой ногой"
            />
            <span class="rating__test-label">Удар правой ногой</span>
          </a>
          <a
            class="rating__test-card"
            href="https://app.junistat.com/player/a983f0d1-3cde-4711-97ed-91694aa8528f/test-results?pageIndex=1&id=dcbdadb9-5e45-4730-b5e6-c25f5bcb4b95&testTag=jump1&from=2024-09-01T00%3A00%3A00.000Z&to=2024-09-01T20%3A59%3A59.999Z&orderDirection=ASC&orderField=h17&tags[]=h17&tags[]=x1002&tags[]=x1003&fromPlayerAcademy=true"
          >
            <Image class="rating__test-icon" src={Jump} alt="Прыжок" />
            <span class="rating__test-label">Прыжок</span>
          </a>
          <div class="rating__tests-banner">
            <span class="example__banner-title"
              >Ещё больше тестов в приложении JuniStat</span
            >
            <div class="example__download">
              <!-- <Image class="example__qr-code" src={qrCode} alt="QR code" /> -->
              <DownloadButtons app="player" place="example" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div id="modal_example" class="modal">
    <!-- Modal content -->
    <div class="modal-content">
      <span class="close">&times;</span>
      <CardProfileExample info={true} lazy={false} flag="ru">
        <div class="example__info" slot="info">
          <h2 class="example__info-title">После проведения тестирований</h2>

          <ul class="example__info-list">
            <li class="example__info-list-item">
              <p>
                У игроков формируются цифровые карточки, как у настоящих
                футболистов в FIFA, с точно посчитанными параметрами силы,
                ловкости, дриблинга, скорости, ударов и тд.
              </p>
            </li>
            <li class="example__info-list-item">
              <p>Узнайте результаты своих тестов.</p>
            </li>
          </ul>
        </div>
      </CardProfileExample>
      <AppButton class="modal__close-button" as="button">Понятно</AppButton>
    </div>
  </div>
</Layout>

<script>
  window.addEventListener("load", () => {
    //#region [Modal]
    const modal = document.querySelector("#modal_example") as HTMLElement;
    const btn = document.querySelector(".hero__button") as HTMLElement;
    const closeButtons = document.querySelectorAll(
      ".close, .modal__close-button"
    ) as NodeListOf<HTMLElement>;
    const body = document.querySelector("body") as HTMLElement;

    if (modal && btn && closeButtons) {
      // Set to true when modal opens
      function openModal() {
        modal.style.display = "block";
        body.style.overflow = "hidden";
      }

      // Set to false when modal closes
      function closeModal() {
        modal.style.display = "none";
        body.style.overflow = "";
      }

      btn.onclick = function () {
        openModal();
      };

      closeButtons.forEach((button) => (button.onclick = closeModal));

      window.onclick = function (event) {
        if (event.target == modal) {
          closeModal();
        }
      };

      // Close modal on esc key
      document.addEventListener("keydown", function (event) {
        if (event.key === "Escape") {
          closeModal();
        }
      });
    }

    //#endregion [Modal]
  });
</script>

<style>
  #hero {
    overflow: visible;
  }

  .hero {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .hero__logo {
    max-height: 256px;
    margin-bottom: 16px;
  }

  .hero__button {
    margin-top: 24px;
  }

  .example__info {
    margin: 0;
    max-width: 490px;
  }

  .example__info-list {
    padding: 0;
  }

  .example__info-list > * + * {
    margin-top: 16px;
  }

  .example__info > * + * {
    margin-top: 24px;
  }

  @media screen and (max-width: 768px) {
    .container.is--academy-selector {
      padding: unset;
    }
  }

  #rating {
    margin-bottom: 88px;
  }

  .rating {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /*#region [Modal]*/
  .modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 100px;
    padding-bottom: 56px;
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0, 0, 0); /* Fallback color */
    background-color: rgba(0, 0, 0, 0.4); /* Black w/ opacity */
  }

  /* Modal Content */
  .modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 24px;
    padding-top: 32px;
    border: 1px solid #888;
    width: 80%;
    border-radius: 16px;
  }

  @media screen and (max-width: 688px) {
    .modal-content {
      padding: 16px;
      padding-bottom: 24px;
      width: 90%;
    }
  }

  /* The Close Button */
  .close {
    color: #aaaaaa;
    float: right;
    font-size: 48px;
    font-weight: bold;
  }

  .close:hover,
  .close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
  }

  .modal__close-button {
    margin-top: 24px;
  }

  @media screen and (min-width: 688px) {
    .modal__close-button {
      margin-left: auto;
    }
  }

  /*#endregion [Modal]*/

  /*#region [Tests]*/
  .rating__tests-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 40px;
    max-width: 480px;
    width: 100%;
  }

  .rating__test-card,
  .rating__tests-banner {
    display: flex;
    align-items: center;
    border-radius: 8px;
    background-color: white;
    padding: 16px;
  }

  .rating__test-card {
    font-weight: 500;
    font-size: 24px;
    line-height: 120%;
    transition: background-color 0.2s ease-in-out;
  }

  .rating__tests-banner {
    margin-top: 16px;
    background-color: var(--primary);
    flex-direction: column;
    font-size: 21px;
    gap: 8px;
    line-height: 120%;
    font-weight: 500;
    justify-content: center;
    text-align: center;
  }

  .download-buttons {
    margin-top: 8px;
  }

  :global(.download-buttons__button) {
    width: 50%;
  }

  @media (any-hover: hover) {
    .rating__test-card:hover {
      background-color: #ecf0f3;
    }

    .rating__test-card:hover .rating__test-icon {
      transform: scale(1.1);
    }
  }

  .rating__test-icon {
    width: 64px;
    height: 64px;
    margin-right: 24px;
    border-radius: 6px;
    transition: transform 0.2s ease-in-out;
  }
  /*#endregion [Tests]*/
</style>
