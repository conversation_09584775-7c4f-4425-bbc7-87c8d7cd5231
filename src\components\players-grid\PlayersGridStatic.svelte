<script lang="ts">
  import { track } from "@amplitude/analytics-browser";

  //#region [i18n]
  import {
    getLangFromUrl,
    useTranslations,
    useTranslatedPath,
  } from "@lang/utils";

  const lang = getLangFromUrl(window.location);
  const t = useTranslations(lang);
  //#endregion [i18n]

  //#region [Components]
  import debounce from "@src/utils/debounce";
  import PlayerCard from "@components/players-grid/PlayerCard.svelte";
  //#endregion [Components]

  //#region [Props]
  //#endregion [Props]

  let totalCount: number = 0;
  let pageSize: number = 8;

  //#region [Get players]
  let fetchPlayersPromise = fetchPlayers();
  // https://app.junistat.com/player/84ebec7c-3c59-41fa-bfdd-e0e00950edca
  // https://app.junistat.com/player/ddde6259-53c1-4cdf-87d8-ba18af2d74bb
  // https://app.junistat.com/player/98772118-c27b-4607-8891-5463dbfd8718
  // https://app.junistat.com/player/8b4d567d-f100-4ef3-8aea-da2ab9a6ca66
  // https://app.junistat.com/player/3e480999-5dfc-4dd7-ad70-fab1975eb5a1
  // https://app.junistat.com/player/4484a8a9-12a8-4964-998d-d4630876a7be
  // https://app.junistat.com/player/4f7fb550-f5d3-4154-9e8e-48bcb7090a5b
  // https://app.junistat.com/player/4bfa94ef-ceac-4ea8-a712-db097fa48262

  async function fetchPlayers() {
    const playerIds = [
      "6381c4a9-dab6-4330-9d0d-d0df55c60e5a",
             "3e480999-5dfc-4dd7-ad70-fab1975eb5a1",

      "55abf8eb-b68a-4dda-a34b-259424950c37",
            "a3982a80-b65c-44cd-bef3-be5f629cbef2",

      "a9fefe91-74ce-433f-a28e-ec68b1e9f79e",
             "7af32db5-6547-4f34-8108-511e9ab1911c",

      "9082f241-d3eb-471b-a7b5-58917adc332c",
       "1a0d1c39-d119-49d4-884e-43085362b565",

      // "4484a8a9-12a8-4964-998d-d4630876a7be",
    ];

    //#region [Cache players data]
    // Check if ids are in local storage same as playerIds
    const storedIds = JSON.parse(localStorage.getItem("playerIds") || "[]") || [
      "",
    ];
    const lastFetch = localStorage.getItem("lastFetch");

    if (lastFetch && playerIds.every((id, i) => storedIds[i] === id)) {
      const lastFetchDate = new Date(lastFetch);
      const now = new Date();
      const timeDiff = now.getTime() - lastFetchDate.getTime();
      const timeDiffMinutes = Math.floor(timeDiff / (1000 * 60));

      // 12 hours
      if (timeDiffMinutes < 720) {
        // console.log("Players data are in local storage");
        return JSON.parse(localStorage.getItem("playersData") || "[]");
      }
    }

    localStorage.setItem("playerIds", JSON.stringify(playerIds));
    //#endregion [Cache players data]

    const fetchPlayerData = async (id: string) => {
      const response = await fetch(
        `https://app.junistat.com/api/public/players/${id}`,
      );
      if (!response.ok) {
        throw new Error(`Error fetching data for player with ID: ${id}`);
      }
      return response.json();
    };

    const fetchAllPlayersData = async (ids: string[]) => {
      const fetchPromises = ids.map((id) =>
        fetchPlayerData(id).then(
          (data) => data,
          (error) => error,
        ),
      );

      const results = await Promise.allSettled(fetchPromises);

      const fulfilledResults = results
        .filter((result) => result.status === "fulfilled" && result.value.id)
        // @ts-ignore
        .map((result) => result.value);

      const rejectedResults = results
        .filter((result) => result.status === "rejected")
        .map((result) => result.reason);

      // Save players data to localstorage
      localStorage.setItem("playersData", JSON.stringify(fulfilledResults));
      // Set date time to localstorage
      localStorage.setItem("lastFetch", new Date().toISOString());

      return fulfilledResults;
    };

    return fetchAllPlayersData(playerIds);
  }
  //#endregion [Get players]

  import Swiper from "swiper";
  import "swiper/css/bundle";
  import { onMount } from "svelte";

  let swiperEl: HTMLDivElement;
  onMount(() => {
    let swiper: null | Swiper = null;
    // Detect screen size change and run this code, use debounce
    window.addEventListener("resize", debounce(runSlider, 500));
    runSlider();

    function runSlider() {
      console.log("slider running :>> ");
      // Run only on 680px
      if (window.matchMedia("(max-width: 680px)").matches) {
        if (!swiper) {
          swiper = new Swiper(swiperEl, {
            slidesPerView: "auto",
            spaceBetween: 24,
          });
        }
      } else {
        console.log("destroy");
        swiper && swiper?.destroy();
        swiper = null;
      }
    }
  });
</script>

<div class="rating__header">
  {#if !$$slots.selector}
    <div class="rating__header-title">
      <div class="" style="display: flex; align-items: center; gap: 16px">
        <!-- prettier-ignore -->
        <svg xmlns="http://www.w3.org/2000/svg" width="65" height="64" fill="none" viewBox="0 0 65 64"><path fill="#FFE83F" d="m58.468 22.352-5.675 26a2 2 0 0 1-1.97 1.653H14.178a2 2 0 0 1-1.97-1.653l-5.675-26A2 2 0 0 1 10 20.687l10.5 11.31L30.685 9.16a2 2 0 0 1 3.63 0L44.5 31.997 55 20.687a2 2 0 0 1 3.468 1.665Z"/><path fill="#181B1E" d="M58.226 18.403a3.962 3.962 0 0 0-4.725.947l-8.417 9.073-8.95-20.073a.26.26 0 0 1 0-.025 4 4 0 0 0-7.265 0 .26.26 0 0 1 0 .025l-8.95 20.073L11.5 19.35a4 4 0 0 0-6.937 3.355c0 .028 0 .053.017.08l5.67 25.968a4 4 0 0 0 3.93 3.25h36.643a4 4 0 0 0 3.927-3.25l5.67-25.968c0-.027 0-.052.018-.08a3.962 3.962 0 0 0-2.213-4.302Zm-7.387 29.52-.015.08H14.179l-.015-.08L8.5 22.003l.035.04 10.5 11.31a2 2 0 0 0 3.295-.545L32.5 10.003l10.173 22.812a2 2 0 0 0 3.295.545l10.5-11.31.032-.047-5.662 25.92Z"/></svg>
        <h2>{t("rating.title_players_month")}</h2>
      </div>
      <p style="margin-top: 16px; max-width: 688px">
        {t("rating.subtitle_players_month")}
      </p>
    </div>
  {/if}
  <slot name="selector" />
</div>

<svg class="svg" style="position:absolute; opacity:0; pointer-events: none;">
  <clipPath id="shield-mask" clipPathUnits="objectBoundingBox"
    ><path
      d="m0.018,0.946,0,0.001,0,0,0,-0.001 m0,0,0,0.001,0,0,0,0,0,0,0.001,0,0.002,0 a2,1,0,0,0,0.035,0.005 c0.023,0.003,0.053,0.007,0.085,0.011 l0,-0.001,0,0.001,0.262,0.029,0,0 c0.049,0.006,0.074,0.008,0.099,0.008 c0.025,0,0.05,-0.003,0.099,-0.008 l0,0,0.26,-0.029 c0.031,-0.004,0.062,-0.007,0.084,-0.011 a2,1,0,0,0,0.035,-0.005 l0.002,0,0.001,0,0,0 h0 l0,0,0,0,0.001,0 c0.001,0,0.002,0,0.003,0 c0.003,0,0.006,-0.001,0.009,-0.002 l0,0,0,0 a0.017,0.011,0,0,0,0.005,-0.007 V0.049 C1,0.023,0.967,0.001,0.925,0.001 H0.079 C0.037,0.001,0.002,0.023,0.002,0.049 v0.888 c0,0.003,0.002,0.005,0.005,0.007 c0.001,0.001,0.002,0.001,0.003,0.001 c0.001,0,0.002,0.001,0.004,0.001 a0.045,0.028,0,0,0,0.004,0.001 l0,0,0,0,0,0,0,0,0,-0.001"
    /></clipPath
  >
</svg>

<div class="swiper" bind:this={swiperEl}>
  <div class="rating__grid swiper-wrapper">
    {#await fetchPlayersPromise}
      {#each Array(pageSize) as i}
        <div class="shield-placeholder" />
      {/each}
    {:then players}
      {#if true}
        {#each players as player}
          <div class="swiper-slide">
            <PlayerCard {player} />
          </div>
        {/each}
      {:else}
        <div class="rating__empty">
          <!-- prettier-ignore -->
          <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" fill="#000000" viewBox="0 0 256 256"><path d="M245,110.64A16,16,0,0,0,232,104H216V88a16,16,0,0,0-16-16H130.67L102.94,51.2a16.14,16.14,0,0,0-9.6-3.2H40A16,16,0,0,0,24,64V208h0a8,8,0,0,0,8,8H211.1a8,8,0,0,0,7.59-5.47l28.49-85.47A16.05,16.05,0,0,0,245,110.64ZM93.34,64l27.73,20.8a16.12,16.12,0,0,0,9.6,3.2H200v16H146.43a16,16,0,0,0-8.88,2.69l-20,13.31H69.42a15.94,15.94,0,0,0-14.86,10.06L40,166.46V64Zm112,136H43.82l25.6-64h48.16a16,16,0,0,0,8.88-2.69l20-13.31H232Z"></path></svg>

          <h3 class="rating__empty-title">{t("rating.grid.empty")}</h3>
        </div>
      {/if}
    {/await}
  </div>
</div>

<style>
  .shield-placeholder {
    display: flex;
    justify-content: center;
    width: 264px;
    height: 425px;
    clip-path: url(#shield-mask);
    background-color: rgba(0, 0, 0, 0.1);
    background-size: cover;
    background-image: linear-gradient(
      146deg,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.5) 50%,
      rgba(255, 255, 255, 0) 70%
    );
    background-position-y: 100%;
    background-size: 100% 200%;
    animation: shield-loader 1s linear infinite reverse;
  }

  @keyframes shield-loader {
    0% {
      background-position-y: 100%;
    }
    100% {
      background-position-y: 300%;
    }
  }

  @media screen and (min-width: 680px) {
    .rating__grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 3rem clamp(16px, 1vw, 90px);
      justify-content: center;
      align-items: center;
      justify-items: center;
    }
  }

  @media screen and (max-width: 1280px) {
    .rating__grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 3rem 3.5rem;
    }
  }

  @media screen and (max-width: 992px) {
    .rating__grid {
      grid-template-columns: 1fr 1fr;
    }
  }

  @media screen and (max-width: 680px) {
    .rating__grid {
      gap: 0;
    }

    .swiper {
      overflow: visible;
    }

    .swiper-slide {
      width: auto;
    }
  }

  /*#region [Empty state]*/
  .rating__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    grid-column: 1 / -1;
  }
  /*#endregion [Empty state]*/
</style>
