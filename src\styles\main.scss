@use "_variables" as v;
@use "_screens" as screen;
@use "_typography";

html {
  // --scroll-behavior: smooth;
  // scroll-behavior: smooth;
}

/* Old styles. Only for non-tailwind */
body {
  font-family:
    "Onest",
    Inter,
    Roboto,
    Helvetica,
    "Helvetica Neue",
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    Arial,
    sans-serif;
  font-size: 18px;
  color: v.$black;
  background-color: v.$gray-light;
  display: flex;
  flex-direction: column;

  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

section {
  overflow: hidden;
}

a {
  color: currentColor;
  text-decoration: none;
}

ol,
ul {
  padding-inline-start: 2rem;
}

.highlight-link {
  text-decoration: underline;
  color: v.$blue;
  text-decoration-thickness: 2px;
  text-underline-offset: 4px;
  margin-right: 4px;

  &:hover {
    text-decoration: none;
  }
}

.section-space {
  margin-top: v.$space-128;

  @media screen and (max-width: screen.$mobile) {
    margin-top: v.$space-80;
  }
}

.section-space-small {
  margin-top: v.$space-48;

  @media screen and (max-width: screen.$mobile) {
    margin-top: v.$space-32;
  }
}

.container {
  padding: 0px v.$space-24;
  width: 100%;
  max-width: 1360px;
  margin: 0 auto;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;

  &:hover {
    opacity: 0.5;
  }
}

*::selection {
  background: v.$black;
  color: v.$primary;
}

.button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  @include v.transition-colors;
}
