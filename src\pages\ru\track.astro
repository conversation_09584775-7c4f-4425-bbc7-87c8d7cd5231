---
import "@styles/main.scss";
import Layout from "@layouts/Layout.astro";

import "@styles/pages/track.css";

//#region [Components]
import AppButton from "@components/AppButton.astro";
//#endregion [Components]

//#region [Built-in components]
import { Image, Picture } from "astro:assets";
//#endregion [Built-in components]

//#region [Images]
import JunitrackHero from "@assets/images/track/junitrack-heatmap.jpg";

// Features
import Record from "@assets/images/track/record.svg";
import Dashboard from "@assets/images/track/dashboard.svg";
import Stats from "@assets/images/track/stats.svg";
import Stream from "@assets/images/track/stream.svg";
import Heatmap from "@assets/images/track/heatmap.jpg";

// Points
import Rise from "@assets/images/track/rise.svg";
import Integration from "@assets/images/track/integration.svg";
import Secure from "@assets/images/track/secure.svg";

//#endregion [Images]
---

<Layout
  contentOnly
  title="JuniTrack | Cистема видеоаналитики"
  description="Открывайте и развивайте таланты мирового уровня с помощью AI платформы JuniTrack"
  ogImage="/og-track.jpg"
>
  <section id="section-hero">
    <div class="container">
      <div class="hero">
        <h1 class="hero__headline">
          Открывайте и развивайте таланты мирового уровня с помощью AI платформы
          JuniTrack
        </h1>
        <div class="hero__description-wrap">
          <p class="hero__desription-text">Анализируйте тренировки</p>
          <p class="hero__desription-text">
            Разбирайте соперников и находите способ выигрывать
          </p>
        </div>
        <AppButton
          as="a"
          href="https://forms.gle/yGckuW4XsQSPUmCn8"
          target="_blank"
          style="mod2"
        >
          <h3>Заказать демонстрацию</h3>
        </AppButton>
      </div>
      <Picture
        class="hero__preview"
        src={JunitrackHero}
        formats={["webp"]}
        densities={[1.5, 2]}
        alt="Поле разделённое на секторы с тепловой карта матча"
        loading="eager"
      />
    </div>
  </section>
  <section id="section-features">
    <div class="container">
      <div class="features">
        <h2 class="features__headline">
          Умная система видеоаналитики на базе технологии JuniStat®. Платформа
          настраивается под требования современных академий и федераций
        </h2>
        <div class="features__grid">
          <div class="features__grid-card">
            <div class="features__card-info">
              <h3>Снимайте тренировки и игры</h3>
              <p>Получайте точные данные по игрокам и командам</p>
            </div>
            <Picture
              class="features__card-image is--record"
              alt="Видеокамера"
              src={Record}
            />
          </div>
          <div class="features__grid-card is--dashboard">
            <Picture
              class="features__card-image is--dashboard"
              alt="Панель управления"
              src={Dashboard}
            />
            <div class="features__card-info">
              <h3>Настраивайте нужные вам метрики</h3>
              <p>Универсальная платформа адаптируется под ваши требования</p>
            </div>
          </div>
          <div class="features__grid-card">
            <div class="features__card-info">
              <h3>Оценивайте эффективность тренировок</h3>
              <p>Применяйте наработанные схемы в игре</p>
            </div>
            <Picture
              class="features__card-image is--stats"
              alt="Отчёт с графиками и пунктами"
              src={Stats}
            />
          </div>
          <div class="features__grid-card">
            <div class="features__card-info">
              <h3>Готовьтесь к играм максимально эффективно</h3>
              <p>
                Находите слабые и сильные стороны соперников быстрее остальных
              </p>
            </div>
            <Picture
              class="features__card-image is--heatmap"
              alt="Тепловая карта"
              src={Heatmap}
            />
          </div>
          <div class="features__grid-card">
            <div class="features__card-info">
              <h3>Транслируйте игры и создавайте контент</h3>
              <p>
                Делитесь лучшими моментами со своей аудиторией и привлекайте
                спонсоров
              </p>
            </div>
            <Picture
              class="features__card-image is--stream"
              alt="Страница видеозаписи матча"
              src={Stream}
            />
          </div>
        </div>
      </div>
    </div>
  </section>
  <section id="section-points">
    <div class="container is--points">
      <div class="points">
        <div class="points__card">
          <div class="points__col">
            <Image class="points__icon" src={Rise} alt="Точки роста" />
            <h3 class="points__card-headline">Точки роста</h3>
          </div>
          <p class="points__card-description">
            Находите новые точки роста для игроков. Следите за их прогрессом
            и сравнивайте с футболистами по всему миру.
          </p>
        </div>
        <div class="points__card">
          <div class="points__col">
            <Image class="points__icon" src={Integration} alt="Интеграции" />
            <h3 class="points__card-headline">Интеграции</h3>
          </div>
          <p class="points__card-description">
            Наша система может интегрироваться с любой цифровой платформой
            через открытый API
          </p>
        </div>
        <div class="points__card">
          <div class="points__col">
            <Image class="points__icon" src={Secure} alt="Безопасность" />
            <h3 class="points__card-headline">Безопасность</h3>
          </div>
          <p class="points__card-description">
            Мы соблюдаем законодательство и обеспечиваем безопасность ваших
            данных
          </p>
        </div>
        <AppButton
          as="a"
          href="https://forms.gle/yGckuW4XsQSPUmCn8"
          target="_blank"
          style="mod2"
        >
          <h3>Заказать демонстрацию</h3>
        </AppButton>
      </div>
    </div>
  </section>
  <section id="section-footer">
    <div class="container">
      <div class="footer">
        <p>ООО «Юнистат» © Москва, Россия</p>
        <p>ОГРН 1207700423776</p>
      </div>
    </div>
  </section>
</Layout>

<script>
  import gsap from "gsap";
  import ScrollTrigger from "gsap/ScrollTrigger";
  gsap.registerPlugin(ScrollTrigger);

  const gridCards = document.querySelectorAll(
    ".features__grid-card, .points__card",
  );

  gridCards.forEach((card) => {
    gsap
      .timeline({
        scrollTrigger: {
          trigger: card,
          start: "top 80%",
          end: "bottom bottom",
        },
      })
      .from(card, { y: "10%", opacity: 0 });
  });
</script>
