---
import "@styles/main.scss";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
//#endregion [i18n]

//#region [Styles]
import "@styles/pages/pricing.scss";
//#endregion [Styles]

//#region [Components]
import Layout from "@layouts/Layout.astro";
//#endregion [Components]

const { currency = "$" } = Astro.props;
---

<Layout noindex={true}>
  <div class="section__pricing-bg">
    <div class="section__bg-1">
      <div class="section__bg-line-4"></div>
      <div class="section__bg-line-3"></div>
      <div class="section__bg-line-2"></div>
      <div class="section__bg-line-1"></div>
    </div>
  </div>
  <div class="section is--hero is--pricing">
    <div class="container is--pricing">
      <div class="pricing">
        <h1 class="pricing__headline">{t("pricing.title")}</h1>
        <div class="pricing__grid">
          <div class="pricing__plans">
            <h3 class="pricing__plans-headline">
              {t("pricing.price_player")}
            </h3>
            <div class="pricing__plans-subtitle">
              {t("pricing.price_player_description")}
            </div>
            <div class="pricing__plans-cards-wrap">
              <div class="pricing__plans-card">
                <h4 class="pricing__plans-card-title">
                  {t("pricing.up_to_1000")}
                </h4>
                <div class="pricing__plans-price-wrap">
                  <p class="pricing__plans-price">
                    {
                      t("pricing.up_to_1000_price_month", {
                        currency: currency,
                      })
                    }
                  </p>
                  <div class="pricing__plans-price-divider"></div>
                  <p class="pricing__plans-price">
                    {
                      t("pricing.up_to_1000_price_year", {
                        currency: currency,
                      })
                    }
                  </p>
                </div>
              </div>
              <div class="pricing__plans-card">
                <h4 class="pricing__plans-card-title">
                  {t("pricing.up_to_3000")}
                </h4>
                <div class="pricing__plans-price-wrap">
                  <p class="pricing__plans-price">
                    {
                      t("pricing.up_to_3000_price_month", {
                        currency: currency,
                      })
                    }
                  </p>
                  <div class="pricing__plans-price-divider"></div>
                  <p class="pricing__plans-price">
                    {
                      t("pricing.up_to_3000_price_year", {
                        currency: currency,
                      })
                    }
                  </p>
                </div>
              </div>
              <div class="pricing__plans-card">
                <h4 class="pricing__plans-card-title">
                  {t("pricing.from_3000")}
                </h4>
                <div class="pricing__plans-price-wrap">
                  <p class="pricing__plans-price">
                    {
                      t("pricing.from_3000_price_month", {
                        currency: currency,
                      })
                    }
                  </p>
                  <div class="pricing__plans-price-divider"></div>
                  <p class="pricing__plans-price">
                    {
                      t("pricing.from_3000_price_year", {
                        currency: currency,
                      })
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="pricing__license">
            <h3 class="pricing__license-headline">
              {t("pricing.license.title")}
            </h3>
            <ul role="list" class="pricing__license-features-list">
              <li class="pricing__license-features-item">
                <div class="pricing__license-features-bullet"></div>
                <p class="pricing__license-features-text">
                  {t("pricing.license.point1")}
                </p>
              </li>
              <li class="pricing__license-features-item">
                <div class="pricing__license-features-bullet"></div>
                <p class="pricing__license-features-text">
                  {t("pricing.license.point2")}
                </p>
              </li>
              <li class="pricing__license-features-item">
                <div class="pricing__license-features-bullet"></div>
                <p class="pricing__license-features-text">
                  {t("pricing.license.point3")}
                </p>
              </li>
              <li class="pricing__license-features-item">
                <div class="pricing__license-features-bullet"></div>
                <p class="pricing__license-features-text">
                  {t("pricing.license.point4")}
                </p>
              </li>
              <li class="pricing__license-features-item">
                <div class="pricing__license-features-bullet"></div>
                <p class="pricing__license-features-text">
                  {t("pricing.license.point5")}
                </p>
              </li>
              <li class="pricing__license-features-item">
                <div class="pricing__license-features-bullet"></div>
                <p class="pricing__license-features-text">
                  {t("pricing.license.point6")}
                </p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>
