---
import "@styles/main.scss";

//#region [Styles]
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
//#endregion [Styles]

//#region [Components]
import Layout from "@layouts/Layout.astro";

import "@styles/pages/ai-test.css";

import TestCard from "@components/ai-test/TestCard.astro";
import AppButton from "@components/AppButton.astro";
import CountrySelector from "@components/ai-test/CountrySelector.astro";
//#endregion [Components]

//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Images]
import BallImg from "@assets/images/ai-test/ball-fit.png";

// Tests icons
import Run15upIcon from "@assets/images/ai-test/tests-icons/15m-run-up.jpg";
import PushUpsIcon from "@assets/images/ai-test/tests-icons/push-ups.jpg";
import ArrowBallIcon from "@assets/images/ai-test/tests-icons/arrow-ball.jpg";
import ArrowIcon from "@assets/images/ai-test/tests-icons/arrow.jpg";
import Dribbling15Icon from "@assets/images/ai-test/tests-icons/dribbling15-ball.jpg";
import jumpIcon from "@assets/images/ai-test/tests-icons/jump-place.jpg";
import LadderFBIcon from "@assets/images/ai-test/tests-icons/ladder-front-back.jpg";
import LadderLRIcon from "@assets/images/ai-test/tests-icons/ladder-left-right.jpg";
import KickIcon from "@assets/images/ai-test/tests-icons/monster-kick-right.jpg";
import Run15Icon from "@assets/images/ai-test/tests-icons/run-15.jpg";
import SerpentIcon from "@assets/images/ai-test/tests-icons/serpient-ball.jpg";

// Posters
import Run15UpPoster from "@assets/images/ai-test/video-posters/poster_15mRunUp-min.jpg";
import Sprint15Poster from "@assets/images/ai-test/video-posters/poster_15mSprint-min.jpg";
import ArrowPoster from "@assets/images/ai-test/video-posters/poster_arrow-min.jpg";
import ArrowBallPoster from "@assets/images/ai-test/video-posters/poster_arrowBall-min.jpg";
import DribblingPoster from "@assets/images/ai-test/video-posters/poster_dribbling-min.jpg";
import JumpPoster from "@assets/images/ai-test/video-posters/poster_jump-min.jpg";
import KickPoster from "@assets/images/ai-test/video-posters/poster_kick-min.jpg";
import LadderBFPoster from "@assets/images/ai-test/video-posters/poster_ladderBF-min.jpg";
import LadderLRPoster from "@assets/images/ai-test/video-posters/poster_ladderLR-min.jpg";
import PushUpsPoster from "@assets/images/ai-test/video-posters/poster_pushups-min.jpg";
import SerpentPoster from "@assets/images/ai-test/video-posters/poster_serpent-min.jpg";

const posters = {
  Run15UpPoster: Run15UpPoster,
  Sprint15Poster: Sprint15Poster,
  ArrowPoster: ArrowPoster,
  ArrowBallPoster: ArrowBallPoster,
  DribblingPoster: DribblingPoster,
  JumpPoster: JumpPoster,
  KickPoster: KickPoster,
  LadderBFPoster: LadderBFPoster,
  LadderLRPoster: LadderLRPoster,
  PushUpsPoster: PushUpsPoster,
  SerpentPoster: SerpentPoster,
};

const optimizedPosters: any = {};
for (const [key, value] of Object.entries(posters)) {
  const optimizedPoster = await getImage({ src: value, format: "webp" });
  optimizedPosters[key] = optimizedPoster;
}

//#endregion [Images]

//#region [Videos]
import m15SprintMP4 from "@assets/videos/ai-tests/15m-sprint.mp4";
import m15SprintFromRunMP4 from "@assets/videos/ai-tests/15m-from-run-up.mp4";
import arrowWithBallMP4 from "@assets/videos/ai-tests/arrow-with-ball.mp4";
import arrowMP4 from "@assets/videos/ai-tests/arrow.mp4";
import dribblingMP4 from "@assets/videos/ai-tests/dribbling.mp4";
import highJumpMP4 from "@assets/videos/ai-tests/jump.mp4";
import ladderBFMP4 from "@assets/videos/ai-tests/ladder-b-f.mp4";
import ladderLRMP4 from "@assets/videos/ai-tests/ladder-l-r.mp4";
import powerKickMP4 from "@assets/videos/ai-tests/power-kick.mp4";
import pushUpsMP4 from "@assets/videos/ai-tests/push-ups.mp4";
import serpentMP4 from "@assets/videos/ai-tests/serpent.mp4";
//#endregion [Videos]

const {lang = "en"} = Astro.props;
---

<Layout
  contentOnly
  title="JuniStat — Smartphone-based AI soccer tests & analysis"
  description=""
  ogImage="https://www.junistat.com/og-tests.jpg"
>
  <section class="section is--hero">
    <div class="container">
      <div class="hero">
        <div class="hero__content">
          <header>
            <div class="header">
              <div class="header__logo-wrap">
                <svg
                  class="header__logo"
                  xmlns="http://www.w3.org/2000/svg"
                  width="227"
                  height="42"
                  fill="none"
                  viewBox="0 0 227 42"
                  ><path
                    fill="#FF651D"
                    d="M92.736 4.18A5.15 5.15 0 0 1 97.795 0h123.347a5.145 5.145 0 0 1 3.984 1.888 5.148 5.148 0 0 1 1.063 4.278l-6.4 31.704a5.147 5.147 0 0 1-5.047 4.13H91.722a5.15 5.15 0 0 1-5.06-6.118L92.737 4.18ZM8.311 36.014c-1.943 0-3.62-.327-5.03-.984C1.872 34.373.778 33.426 0 32.187l3.901-3.719c1.118 1.8 2.613 2.698 4.484 2.698 2.284 0 3.693-1.325 4.228-3.974L15.056 14.8h-8.93l.947-4.739h14.8L18.481 26.9c-.631 3.258-1.775 5.59-3.426 7-1.653 1.41-3.901 2.114-6.745 2.114Zm37.547-20.049-3.901 19.612h-5.394l.4-2.115a8.67 8.67 0 0 1-2.916 1.823c-1.112.393-2.284.59-3.463.585-2.138 0-3.851-.585-5.14-1.751-1.264-1.166-1.895-2.807-1.895-4.921 0-.802.084-1.591.255-2.37l2.151-10.863h5.687l-2.078 10.498a6.826 6.826 0 0 0-.146 1.422c0 2.042 1.094 3.062 3.28 3.062 1.434 0 2.613-.413 3.536-1.238.948-.852 1.591-2.116 1.932-3.791l2.006-9.953h5.686Zm16.678-.292c2.186 0 3.923.585 5.211 1.751 1.289 1.142 1.932 2.781 1.932 4.92a11 11 0 0 1-.255 2.37l-2.186 10.863H61.55l2.116-10.499c.1-.442.148-.895.144-1.348 0-1.02-.279-1.798-.838-2.333-.559-.535-1.396-.801-2.514-.801-1.459 0-2.673.425-3.647 1.274-.947.826-1.59 2.078-1.931 3.755l-1.968 9.952h-5.687l3.9-19.612h5.396l-.437 2.116c1.749-1.605 3.899-2.408 6.452-2.408Zm13.793.292h5.65l-3.901 19.612h-5.65l3.9-19.612Zm3.79-2.734c-.971 0-1.773-.28-2.406-.838a2.825 2.825 0 0 1-.91-2.114c0-.972.34-1.773 1.02-2.406.706-.656 1.615-.984 2.734-.984.997 0 1.799.28 2.406.84.631.532.948 1.201.948 2.003 0 1.046-.353 1.896-1.058 2.553-.704.63-1.615.946-2.733.946Z"
                  ></path><path
                    fill="#fff"
                    d="M109.808 35.326c-2.55 0-4.786-.46-6.706-1.384-1.896-.924-3.354-2.224-4.374-3.901-1.022-1.676-1.533-3.609-1.533-5.797 0-2.89.657-5.49 1.97-7.799 1.336-2.334 3.208-4.167 5.612-5.504 2.407-1.336 5.166-2.005 8.275-2.005 2.285 0 4.302.388 6.051 1.166 1.75.777 3.074 1.895 3.974 3.354l-4.264 3.499c-1.362-1.992-3.414-2.988-6.161-2.988-1.872 0-3.525.437-4.958 1.31a9.127 9.127 0 0 0-3.354 3.573c-.777 1.507-1.166 3.184-1.166 5.031 0 1.968.619 3.537 1.86 4.703 1.264 1.142 3.025 1.713 5.285 1.713 2.697 0 4.957-.984 6.781-2.953l3.498 3.572c-2.527 2.941-6.123 4.41-10.79 4.41Zm22.523-.146c-1.97 0-3.707-.363-5.214-1.092-1.483-.73-2.637-1.75-3.462-3.062-.802-1.336-1.204-2.88-1.204-4.63 0-2.162.511-4.108 1.532-5.831a11.008 11.008 0 0 1 4.228-4.083c1.798-.998 3.827-1.495 6.087-1.495 1.994 0 3.731.364 5.214 1.094 1.481.73 2.623 1.761 3.426 3.098.826 1.312 1.239 2.843 1.239 4.592 0 2.164-.511 4.108-1.531 5.833-.996 1.725-2.407 3.086-4.228 4.083-1.799.996-3.827 1.493-6.087 1.493Zm.364-4.7c1.093 0 2.078-.269 2.952-.802a5.682 5.682 0 0 0 2.042-2.298c.485-.972.729-2.09.729-3.354 0-1.335-.389-2.393-1.166-3.17-.779-.778-1.872-1.167-3.282-1.167-1.118 0-2.114.28-2.988.838a5.42 5.42 0 0 0-2.042 2.26c-.486.972-.729 2.09-.729 3.354 0 1.337.389 2.393 1.166 3.172.802.777 1.908 1.166 3.318 1.166Zm37.045-15.203-3.9 19.612h-5.395l.364-1.931c-1.627 1.483-3.632 2.224-6.015 2.224-1.53 0-2.939-.353-4.227-1.058a8.04 8.04 0 0 1-3.098-3.026c-.754-1.336-1.13-2.903-1.13-4.702 0-2.162.473-4.108 1.42-5.831.972-1.751 2.285-3.112 3.938-4.083a10.386 10.386 0 0 1 5.468-1.495c3.013 0 5.139.984 6.379 2.951l.509-2.66h5.687ZM156.545 30.48c1.118 0 2.114-.268 2.988-.801a5.66 5.66 0 0 0 2.042-2.298c.487-.972.729-2.09.729-3.354 0-1.335-.401-2.393-1.202-3.17-.778-.778-1.872-1.167-3.282-1.167-1.118 0-2.114.28-2.988.838a5.41 5.41 0 0 0-2.041 2.26c-.487.972-.73 2.09-.73 3.354 0 1.337.389 2.393 1.166 3.172.802.777 1.908 1.166 3.318 1.166Zm24.912 4.701c-2.016 0-3.779-.363-5.286-1.092-1.507-.73-2.673-1.75-3.498-3.062-.802-1.336-1.205-2.88-1.205-4.63 0-2.162.511-4.108 1.533-5.831a10.934 10.934 0 0 1 4.264-4.083c1.823-.998 3.875-1.495 6.161-1.495 2.09 0 3.888.437 5.394 1.312a8.016 8.016 0 0 1 3.427 3.647l-4.811 2.404c-.852-1.775-2.309-2.66-4.374-2.66-1.118 0-2.128.278-3.026.837a5.657 5.657 0 0 0-2.078 2.26c-.487.972-.729 2.078-.729 3.318 0 1.337.401 2.405 1.202 3.208.802.777 1.92 1.166 3.354 1.166 2.09 0 3.693-.875 4.811-2.624l4.192 2.624c-.948 1.481-2.246 2.637-3.899 3.463-1.629.825-3.439 1.238-5.432 1.238Zm26.351-20.193c2.188 0 3.925.583 5.213 1.75 1.289 1.165 1.932 2.82 1.932 4.956 0 .78-.084 1.557-.255 2.334L212.51 34.89h-5.686l2.114-10.497a5.84 5.84 0 0 0 .146-1.348c0-1.022-.279-1.8-.838-2.335-.559-.533-1.398-.801-2.516-.801-1.457 0-2.673.425-3.645 1.276-.948.826-1.591 2.078-1.931 3.753l-1.968 9.952h-5.687l5.395-27.047h5.686l-1.822 9.223c1.676-1.385 3.693-2.078 6.05-2.078Z"
                  ></path></svg
                >
              </div>
              <p class="header__description">
                powered by JuniStat<sup>®</sup> AI technology
              </p>
            </div>
          </header>
          <h1 class="hero__headline">
            JuniCoach<sup>®</sup> app helps soccer clubs and associations assess
            player performance and growth using smartphone AI-powered tests
          </h1>

          <p class="hero__subtitle h3">
            Accurate and simple. Used for talent ID, scouting and recruiting,
            and secure player profiling
          </p>
        </div>
      </div>
    </div>
    <div class="container is--hero-bg">
      <Image
        class="hero__bg-image"
        src={BallImg}
        densities={[1.5, 2]}
        loading="eager"
        alt="Ball with red shapes"
      />
    </div>
    <div class="hero__gradient"></div>
  </section>
  <section>
    <div class="container is--tests swiper">
      <div class="tests is--preview swiper-wrapper">
        <!-- Tests -->
        <TestCard
lang={lang}
          testName="15m Sprint"
          type="large"
          video={m15SprintMP4}
          categories={["pace", "agility"]}
          icon={Run15Icon}
          poster={optimizedPosters["Sprint15Poster"].src}
        />
        <!-- 3 cards -->
        <TestCard
lang={lang}
          testName="Coordination ladder left-right"
          type="small"
          video={ladderLRMP4}
          categories={["pace", "agility"]}
          icon={LadderLRIcon}
          poster={optimizedPosters["LadderLRPoster"].src}
        />
        <TestCard
lang={lang}
          testName="Coordination ladder back-forth"
          type="small"
          video={ladderBFMP4}
          categories={["pace", "agility"]}
          icon={LadderFBIcon}
          poster={optimizedPosters["LadderBFPoster"].src}
        />
        <TestCard
lang={lang}
          testName="High jump"
          type="small"
          video={highJumpMP4}
          categories={["physical", "agility"]}
          icon={jumpIcon}
          poster={optimizedPosters["JumpPoster"].src}
        />
        <!-- 3 cards End-->

        <!-- 2 cards -->
        <TestCard
lang={lang}
          testName="Serpent"
          type="medium"
          video={serpentMP4}
          categories={["dribbling"]}
          icon={SerpentIcon}
          poster={optimizedPosters["SerpentPoster"].src}
        />
        <TestCard
lang={lang}
          testName="Power kick. RF/LF"
          type="medium"
          video={powerKickMP4}
          categories={["shooting"]}
          icon={KickIcon}
          poster={optimizedPosters["KickPoster"].src}
        />
        <!-- 2 cards End-->
        <TestCard
lang={lang}
          testName="15m sprint from run up"
          type="large"
          video={m15SprintFromRunMP4}
          categories={["pace"]}
          icon={Run15upIcon}
          poster={optimizedPosters["Run15UpPoster"].src}
        />
        <TestCard
lang={lang}
          testName="Arrow"
          type="large"
          video={arrowMP4}
          categories={["agility"]}
          icon={ArrowIcon}
          poster={optimizedPosters["ArrowPoster"].src}
        />
        <TestCard
lang={lang}
          testName="Dribbling 15m with run up. RF/LF"
          type="large"
          video={dribblingMP4}
          categories={["dribbling"]}
          icon={Dribbling15Icon}
          poster={optimizedPosters["DribblingPoster"].src}
        />
        <TestCard
lang={lang}
          testName="Arrow with the ball. RF/LF"
          type="large"
          video={arrowWithBallMP4}
          categories={["dribbling"]}
          icon={ArrowBallIcon}
          poster={optimizedPosters["ArrowBallPoster"].src}
        />
        <TestCard
lang={lang}
          testName="Push ups"
          type="large"
          video={pushUpsMP4}
          categories={["physical"]}
          icon={PushUpsIcon}
          poster={optimizedPosters["PushUpsPoster"].src}
        />

        <!-- Tests End -->
      </div>
      <div class="tests__pagination-wrap">
        <div class="tests__pagination">
          <button class="button tests__pagination-button is--prev">Prev</button>
          <p class="tests__pagination-counter p2">0 / 00</p>
          <button class="button tests__pagination-button is--next">Next</button>
        </div>
      </div>
      <div class="tests__show-more-wrap">
        <button id="show-more-tests">
          <svg
            class="tests__show-more-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="none"
            viewBox="0 0 32 32"
            ><path
              fill="#000"
              d="M28 17 16 29 4 17h6V6a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v11h6Z"
              opacity=".2"></path><path
              fill="#000"
              d="M28.924 16.617a1 1 0 0 0-.923-.617h-5V6a2 2 0 0 0-2-2H11a2 2 0 0 0-2 2v10H4a1 1 0 0 0-.708 1.707l12 12a1.001 1.001 0 0 0 1.415 0l12-12a1 1 0 0 0 .216-1.09Zm-12.923 10.97L6.415 18H10a1 1 0 0 0 1-1V6h10v11a1 1 0 0 0 1 1h3.586L16 27.586Z"
            ></path></svg
          >
          Show all tests
        </button>
      </div>
    </div>
  </section>
  <section class="section-space">
    <div class="container">
      <div class="features">
        <h2 class="features__headline">
          Unlock 14 tests in the app assessing 70+ technical and physical
          performance attributes
        </h2>
        <div class="features__wrap">
          <div class="features__card">
            <svg
              class="features__card-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="128"
              height="128"
              fill="none"
              viewBox="0 0 128 128"
              ><path
                fill="#212121"
                d="M68.321 69.335H21.335A10.667 10.667 0 0 0 10.668 80v8l.027 1.227C11.548 106.902 30.817 112 45.335 112c5.493 0 11.674-.73 17.274-2.597a34.518 34.518 0 0 1-3.941-16.07c0-9.311 3.675-17.77 9.653-24Zm1.014-34.667a24 24 0 1 0-48 0 24 24 0 0 0 48 0ZM112 40.001a18.665 18.665 0 0 0-31.866-13.199A18.667 18.667 0 1 0 112 40.002Zm10.667 53.334a29.33 29.33 0 0 1-8.592 20.741 29.33 29.33 0 0 1-41.483 0 29.333 29.333 0 1 1 50.075-20.741ZM111.223 80.78a2.668 2.668 0 0 0-3.776 0l-19.446 19.451-8.778-8.784a2.67 2.67 0 0 0-3.776 3.776l10.666 10.666a2.656 2.656 0 0 0 2.91.58 2.65 2.65 0 0 0 .866-.58l21.334-21.333a2.663 2.663 0 0 0 .783-1.888 2.657 2.657 0 0 0-.783-1.888Z"
              ></path></svg
            >
            <h3 class="features__card-headline">
              Scientifically proven metrics to impact soccer skills
            </h3>
          </div>
          <div class="features__card">
            <svg
              class="features__card-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="128"
              height="128"
              fill="none"
              viewBox="0 0 128 128"
              ><path
                fill="#212121"
                d="M63.999 10.668a21.333 21.333 0 0 1 21.333 21.333v10.667h13.333a8 8 0 0 1 8 8v58.667a7.997 7.997 0 0 1-8 8H29.332a7.998 7.998 0 0 1-8-8V50.668a8 8 0 0 1 8-8h13.333V32.001A21.333 21.333 0 0 1 64 10.668Zm0 61.333a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm0-50.666A10.667 10.667 0 0 0 53.332 32v10.667h21.333V32.001A10.667 10.667 0 0 0 64 21.335Z"
              ></path></svg
            >
            <h3 class="features__card-headline">Secure data collection</h3>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="overflow-visible section-space">
    <div class="container">
      <div class="price">
        <div class="price__header">
          <h2 class="price__headline">
            The most accessible<br />AI-testing protocol
          </h2>
        </div>
        <div class="price__content">
          <div class="price__grid">
            <div class="price__card" style="grid-area: card1;">
              <div class="price__price-cell">
                <div class="price__cell-head">
                  <h3>Authorized distributors</h3>
                </div>
                <div class="price__distr-country-wrap">
                  <div class="price__distr-country-name h4">USA</div>
                  <div class="price__distr-list">
                    <a
                      class="text-link"
                      href="https://skills-u.com/contacts"
                      target="_blank">skills-u.com</a
                    >
                  </div>
                </div>
                <div class="price__distr-country-wrap">
                  <div class="price__distr-country-name h4">Canada</div>
                  <div class="price__distr-list">
                    <a
                      class="text-link"
                      href="https://www.ianmcclurg.com/"
                      target="_blank">ianmcclurg.com</a
                    >
                    <a
                      class="text-link"
                      href="https://xtratime.ca/contact"
                      target="_blank">xtratime.ca</a
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="price__card" style="grid-area: form;">
              <h3>Experience world-class talent development. Book a demo</h3>
              <form class="price__form">
                <label for="name">
                  <span class="price__form-label">Name</span>
                  <input
                    class="price__form-input"
                    type="text"
                    name="name"
                    id="name"
                  />
                </label>
                <CountrySelector />
                <label for="phone">
                  <span class="price__form-label">Phone number</span>
                  <input
                    class="price__form-input"
                    type="tel"
                    name="phone"
                    id="phone"
                  />
                </label>
                <label for="email">
                  <span class="price__form-label"
                    >Email<sup style="color: red">*</sup></span
                  >
                  <input
                    required
                    class="price__form-input"
                    type="email"
                    name="email"
                    id="email"
                  />
                </label>
                <span class="price__form-agrement" style="display: block"
                  >By submitting this form, you agree to the <a
                    class="text-link"
                    href="https://junistatrating.notion.site/JuniStat-Privacy-Policy-cdabd544b7bf4a7d83628a173525247b"
                    target="_blank"
                    rel="">Privacy Policy</a
                  ></span
                >
                <AppButton
                  as="button"
                  type="submit"
                  style="mod2"
                  skew={true}
                  id="form_button"
                >
                  <span style="font-size: 26px">Book a Demo</span>
                </AppButton>
              </form>
              <div class="form__error">
                We're sorry! It looks like your submission didn't go through.
                Please try resubmitting later. If you continue to have issues,
                let us <NAME_EMAIL>.
              </div>
              <div class="form__success">
                Thank you. Your application has been sent. Our manager will
                contact you shortly
              </div>
            </div>
          </div>
        </div>
        <div class="price__cards">
          <div class="price__card-user is--free-tests">
            <svg
              class="price__card-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="96"
              height="96"
              fill="none"
              viewBox="0 0 96 96"
              ><path
                fill="#000"
                d="M71.084 3.844H24.917a3.773 3.773 0 0 0-3.773 3.773v.124a3.773 3.773 0 0 0 3.773 3.773h46.167a3.773 3.773 0 0 0 3.772-3.773v-.124a3.773 3.773 0 0 0-3.772-3.773Z"
              ></path><path
                fill="#000"
                d="M30.67 56.327c-.598 3.1-2.498 4.814-5.702 5.14-1.584.164-2.462-1.962-1.512-3.143.793-.984 1.83-.461 2.722-1.143 1.959-1.502-1.128-4.627-2.602-6.12a2691.35 2691.35 0 0 1-9.94-10.099C8.322 35.543 1.482 27.037 4.684 18.801c3.09-7.95 13.185-9.653 19.06-3.62a.573.573 0 0 0 .409.173c15.872.013 31.74.015 47.606.005.26-.003.46-.091.6-.264 2.347-2.885 6.931-3.965 10.075-3.413 7.757 1.359 11.554 9.015 8.842 16.445-1.31 3.584-3.568 7.094-6.778 10.531-3.309 3.546-7.39 7.704-12.245 12.475-1.2 1.178-2.228 2.597-3.086 4.258a.588.588 0 0 0-.058.384c.485 2.53 2.184 1.2 3.408 2.477 1.604 1.665-.518 3.614-2.568 3.072-2.627-.695-4.174-2.381-4.641-5.06-.045-.249-.116-.255-.212-.019C61.214 65.65 51.773 82.617 39.8 71.01c-3.955-3.83-6.935-9.614-8.923-14.702-.09-.234-.158-.227-.206.019Zm-8.03-35.755c-2.827-8.199-14.846-6.13-14.942 2.433-.053 4.493 3.638 9.456 6.5 12.836 4.04 4.761 10.113 9.844 14.72 15.494a.039.039 0 0 0 .068-.038 118.861 118.861 0 0 1-5.482-26.439c-.028-.33-.123-.734-.283-1.214-.326-.994-.302-2.266-.58-3.072Zm44.386 30.907c3.778-4.843 8.587-9.043 12.85-13.474 3.59-3.724 8.222-9.796 8.438-14.659.389-8.654-11.41-11.107-14.918-2.87-.394.926-.341 2.073-.581 3.1-.243 1.047-.389 1.818-.437 2.314a116.443 116.443 0 0 1-5.448 25.536.057.057 0 0 0 .029.061.058.058 0 0 0 .067-.008ZM46.218 33.604v14.165c0 .153.077.23.23.23h5.093c.199-.003.298-.104.298-.302V25.319c0-.24-.12-.36-.36-.36h-3.696a.36.36 0 0 0-.365.25c-1.123 2.934-3.282 4.366-6.475 4.296-.17-.004-.255.078-.255.244v3.37c0 .154.077.23.23.23h5.046c.17 0 .254.085.254.255Z"
              ></path><path
                fill="#000"
                d="M40.321 84.23v-7.666c0-.22.095-.272.284-.154 4.966 3.127 9.913 3.093 14.841-.1.157-.1.235-.056.235.13v7.785c0 .157.08.237.24.24 2.679.038 5.933-.322 8.223.537 2.96 1.114 4.624 3.407 4.992 6.879.022.185-.061.278-.25.278H27.112c-.186 0-.27-.094-.25-.283.355-3.398 1.943-5.662 4.762-6.792 2.342-.94 5.692-.6 8.453-.61.163 0 .244-.081.244-.244Z"
              ></path></svg
            >
            <h3 class="price__card-title">Organizations</h3>
            <p class="price__card-text">Federations, Clubs, Colleges, etc.</p>
            <AppButton
              id="sign-up-org"
              class="price__card-button"
              as="a"
              style="mod2"
              skew
              href="https://app.junistat.com/sign-up?role=academy"
              target="_blank"
            >
              Sign Up
            </AppButton>
          </div>
          <div class="price__card-user is--free-tests">
            <svg
              class="price__card-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="96"
              height="96"
              fill="none"
              viewBox="0 0 96 96"
              ><path
                fill="#000"
                d="M48.028 30.61c7.417 0 13.43-6.013 13.43-13.43S55.446 3.75 48.029 3.75s-13.43 6.013-13.43 13.43c0 7.418 6.013 13.43 13.43 13.43Zm-17.04 51.451c-1.745 2.73-3.916 3.594-6.514 2.592-.954-.368-2.076-1.206-3.365-2.515a672.363 672.363 0 0 0-8.4-8.405c-3.402-3.344-3.87-7.118-1.406-11.323a281.89 281.89 0 0 1 10.224-16.171c1.523-2.237 2.905-3.83 4.147-4.781 3.02-2.31 6.568-3.365 10.642-3.163a.36.36 0 0 1 .34.24l8.612 20.558c.063.16.065.322.004.485l-2.952 7.886a1.124 1.124 0 0 0 .687 1.455l5.74 1.977c.303.103.632.084.92-.052.288-.136.51-.379.62-.677l3.405-9.096a1.232 1.232 0 0 0-.754-1.594l-1.777-.605a.255.255 0 0 1-.166-.23.23.23 0 0 1 .018-.096l8.366-20.055a.342.342 0 0 1 .116-.145.324.324 0 0 1 .172-.061c6.42-.227 11.35 2.3 14.794 7.584a588.55 588.55 0 0 1 9.95 15.826c2.328 3.84 2.837 7.948-.523 11.39-2.909 2.982-5.816 5.965-8.722 8.947-1.539 1.578-2.817 2.559-3.835 2.943-2.39.905-4.48.109-6.268-2.39-.122-.174-.19-.154-.207.057l-.499 7.262c-.01.157-.094.235-.254.235H31.948c-.16 0-.245-.08-.255-.24l-.537-7.795c-.013-.182-.07-.197-.168-.043Zm-2.132-26.098c-.022-.259-.112-.283-.268-.072L20.73 66.677a.273.273 0 0 0 .029.36c3.122 3.106 6.234 6.223 9.336 9.35.07.074.13.17.182.288.051.132.115.245.192.341a.136.136 0 0 0 .152.036.135.135 0 0 0 .083-.132l-1.848-20.957Zm38.607-.076a.144.144 0 0 0-.26.072l-1.847 20.817a.142.142 0 0 0 .065.134.146.146 0 0 0 .184-.023l9.782-9.932a.146.146 0 0 0 .042-.09.145.145 0 0 0-.028-.097l-7.938-10.881Z"
              ></path><path
                fill="#000"
                d="m55.707 40.532-7.57 18.13a.159.159 0 0 1-.056.068.157.157 0 0 1-.17 0 .156.156 0 0 1-.056-.069l-7.594-18.13a.154.154 0 0 1 .14-.21h15.167a.155.155 0 0 1 .14.21Z"
              ></path></svg
            >
            <h3 class="price__card-title">Coaches and scouts</h3>
            <AppButton
              id="sign-up-coach"
              class="price__card-button"
              as="a"
              style="mod2"
              skew
              href="https://junistatapp.page.link/coach"
              target="_blank"
            >
              Sign Up
            </AppButton>
          </div>
          <div class="price__card-user">
            <svg
              class="price__card-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="96"
              height="96"
              fill="none"
              viewBox="0 0 96 96"
              ><path
                fill="#000"
                d="M96 22.713v.802l-7.81 19.483a1.875 1.875 0 0 1-2.405 1.06l-8.755-3.292a.178.178 0 0 0-.158.02.167.167 0 0 0-.072.139V90.24a1.92 1.92 0 0 1-1.92 1.92H21.12a1.92 1.92 0 0 1-1.92-1.92V40.987c0-.205-.096-.27-.288-.197l-8.611 3.235a1.96 1.96 0 0 1-1.484-.042 1.97 1.97 0 0 1-1.031-1.071L0 23.467v-.816c.235-.49.5-.94.994-1.238 9.683-5.805 19.384-11.628 29.102-17.468a.776.776 0 0 1 .389-.105h34.934c.192 0 .38.051.543.149a6756.401 6756.401 0 0 1 28.896 17.299c.544.326.924.801 1.142 1.425ZM61.45 7.943a.158.158 0 0 0-.12-.263H34.68a.159.159 0 0 0-.12.264l13.325 14.961a.157.157 0 0 0 .186.041.177.177 0 0 0 .054-.04L61.45 7.943ZM10.944 20.132a.177.177 0 0 0-.254-.086l-2.895 1.708c-.153.09-.197.218-.13.384 1.399 3.517 2.81 7.03 4.234 10.541 1.301 3.202 4.666 1.363 3.778-.84a27466.2 27466.2 0 0 1-4.733-11.707Zm69.653 11.035c-1.291 3.269 2.347 4.363 3.245 2.155a1769.17 1769.17 0 0 0 4.492-11.174c.064-.166.02-.294-.134-.384l-2.86-1.709c-.164-.099-.282-.059-.356.12a1947.62 1947.62 0 0 0-4.387 10.992ZM37.867 55.83c-1.197.448-1.617 1.233-1.262 2.356.552 1.748 3.192 1.359 4.54 1.344 1.015-.01 1.727-.126 2.137-.35 1.324-.734 1.113-3.115-.538-3.37a.585.585 0 0 1-.35-.194.568.568 0 0 1-.14-.372c-.012-4.362-.027-8.656-.043-12.883-.01-2.28-2.616-2.333-4.166-2.011-1.872.393-2.165 3.23-.197 3.657a.701.701 0 0 1 .552.687l-.01 10.382a.806.806 0 0 1-.523.754Zm21.586-10.047a5.371 5.371 0 0 0-5.371-5.371h-.653a5.371 5.371 0 0 0-5.371 5.371v8.275a5.371 5.371 0 0 0 5.37 5.372h.654a5.371 5.371 0 0 0 5.37-5.372v-8.275Z"
              ></path><path
                fill="#000"
                d="M53.82 44.184h-.124a1.848 1.848 0 0 0-1.848 1.848v7.776c0 1.02.827 1.848 1.848 1.848h.125c1.02 0 1.848-.828 1.848-1.848v-7.776a1.848 1.848 0 0 0-1.848-1.848Z"
              ></path></svg
            >
            <h3 class="price__card-title">Parents and players</h3>
            <AppButton
              id="sign-up-player"
              class="price__card-button"
              as="a"
              style="mod2"
              skew
              href="https://app.junistat.com/sign-up?role=mentor"
              target="_blank"
            >
              Sign Up
            </AppButton>
          </div>
        </div>
        <footer class="section-space">
          <div class="container is--footer">
            <div class="footer">
              <p>JuniStat Corp © Delaware, USA</p><a
                href="/privacy/"
                target="_blank"
                referrerpolicy="no-referrer"
                rel="nofollow"
              >
                Privacy policy
              </a>
            </div>
          </div>
        </footer>
      </div>
    </div>
  </section>
</Layout>

<script>
  import { Swiper } from "swiper";
  import { Navigation, Pagination } from "swiper/modules";
  // import Swiper and modules styles

  import { track } from "@amplitude/analytics-browser";

  //#region [Tracking]
  const regButtons = document.querySelectorAll(
    ".price__card-button"
  ) as NodeListOf<HTMLElement>;

  regButtons.forEach((button) => {
    button.addEventListener("click", () => {
      track("Click registration button", {
        academy: "Tests",
        button: button?.id,
      });
    });
  });

  //#endregion [Tracking]

  //#region [Show all tests]
  const showAllTriggerEl = document.querySelector(
    ".tests__show-more-wrap"
  ) as HTMLElement;
  const testGrid = document.querySelector(".tests") as HTMLElement;

  if (showAllTriggerEl && testGrid) {
    showAllTriggerEl.addEventListener("click", () => {
      testGrid.classList.remove("is--preview");
      showAllTriggerEl.style.display = "none";
    });
  }
  //#endregion [Show all tests]

  //#region [Tests slider]
  const isMobile = matchMedia("(max-width: 689px)").matches;

  if (isMobile) {
    const testsSlider = new Swiper(".container.swiper", {
      modules: [Navigation, Pagination],
      spaceBetween: 16,
      autoHeight: true,
      slidesPerView: "auto",
      slideToClickedSlide: true,
      // If we need pagination
      pagination: {
        el: ".tests__pagination-counter",
        type: "fraction",
      },

      // Navigation arrows
      navigation: {
        nextEl: ".tests__pagination-button.is--next",
        prevEl: ".tests__pagination-button.is--prev",
      },
    });
  }

  //#endregion [Tests slider]

  //#region [Form]

  const form = document.querySelector(".price__form") as HTMLFormElement;

  form.addEventListener("submit", async (e) => {
    e.preventDefault();
    const formData = new FormData(form);

    const userEmail = formData.get("email");
    const userPhone = formData.get("phone");
    const userName = formData.get("name");
    const userCountry = formData.get("country");

    try {
      const sendData = await fetch(
        "https://junistat-request-demo-bot.pages.dev" + "/api/submit",
        {
          method: "POST",
          body: JSON.stringify({
            name: userName,
            country: userCountry,
            phone: userPhone,
            email: userEmail,
          }),
        }
      );

      const successMessage = document.querySelector(
        ".form__success"
      ) as HTMLElement;

      successMessage.style.display = "block";
      form.style.display = "none";

      track("Submit Demo request form", {
        status: true,
        name: userName,
        country: userCountry,
        phone: userPhone,
        email: userEmail,
      });
    } catch (error) {
      const errorMessage = document.querySelector(
        ".form__error"
      ) as HTMLElement;
      errorMessage.style.display = "block";

      track("Submit Demo request form", {
        status: false,
        name: userName,
        country: userCountry,
        phone: userPhone,
        email: userEmail,
      });
    }
  });
  //#endregion [Form]
</script>
