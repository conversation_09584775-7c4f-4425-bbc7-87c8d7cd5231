---
//#region [i18n]
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]

//#region [Styles]
import "@styles/main.scss";
import "@styles/pages/rating.scss";
//#endregion [Styles]

//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import PlayersGrid from "@components/players-grid/PlayersGrid.svelte";
import CardProfileExample from "@components/CardProfileExample.astro";
import GridAcademySelector from "@components/players-grid/GridAcademySelector.svelte";
import AppButton from "@components/AppButton.astro";
//#endregion [Components]

//#region [Images]
import Logo from "@assets/images/academies/zenit-championika/zenit-championika-logo.svg";
//#endregion [Images]
---

<Layout>
  <section id="hero" class="section-space-small">
    <div class="container">
      <div class="hero">
        <Image class="hero__logo" src={Logo} alt="Zenit-Championika" />
        <h1>Zenit-Championika</h1>
        <AppButton class="hero__button" typeStyle="secondary" as="button">
          <svg
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            ><path
              d="M12 1.999c5.524 0 10.002 4.478 10.002 10.002 0 5.523-4.478 10.001-10.002 10.001-5.524 0-10.002-4.478-10.002-10.001C1.998 6.477 6.476 1.999 12 1.999Zm-.004 8.25a1 1 0 0 0-.992.885l-.007.116.003 5.502.007.117a1 1 0 0 0 1.987-.002L13 16.75l-.003-5.501-.007-.117a1 1 0 0 0-.994-.882ZM12 6.5a1.251 1.251 0 1 0 0 2.503A1.251 1.251 0 0 0 12 6.5Z"
              fill="#212121"></path></svg
          >
          How it works
        </AppButton>
      </div>
    </div>
  </section>

  <section id="rating" class="section-space-small">
    <div class="container">
      <div class="rating">
        <PlayersGrid client:only="svelte">
          <GridAcademySelector
            slot="selector"
            client:only="svelte"
            lang={"en"}
            academies={[
              {
                name: "All players",
                franchiseId: "7a71513b-fb9c-41d3-8c74-d5689b1bb76f",
                academyId: "",
              },
            ]}
          />
        </PlayersGrid>
      </div>
    </div>
  </section>

  <div id="modal_example" class="modal">
    <!-- Modal content -->
    <div class="modal-content">
      <span class="close">&times;</span>
      <CardProfileExample info={true} lazy={false} flag="es">
        <div class="example__info" slot="info">
          <h2 class="example__info-title">After testing</h2>

          <ul class="example__info-list">
            <li class="example__info-list-item">
              <p>
                Players receive digital cards, like real football players in
                FIFA, with precisely calculated parameters for strength,
                agility, dribbling, speed, shooting, etc.
              </p>
            </li>
            <li class="example__info-list-item">
              <p>
                Parents receive development recommendations, while coaches get
                reports and training recommendations.
              </p>
            </li>
            <li class="example__info-list-item">
              <p>
                Players with high results catch the attention of academies and
                scouts and may receive invitations for tryouts.
              </p>
            </li>
          </ul>
        </div>
      </CardProfileExample>
      <AppButton class="modal__close-button" as="button">Close</AppButton>
    </div>
  </div>
</Layout>

<script>
  import { track } from "@amplitude/analytics-browser";

  window.addEventListener("load", () => {
    //#region [Track footer cards]
    document
      .querySelector("#go-to-parents-card")
      ?.addEventListener("click", () => {
        track("Click Parens card in footer");
      });

    document
      .querySelector("#go-to-clubs-card")
      ?.addEventListener("click", () => {
        track("Click Clubs card in footer");
      });
    //#endregion [Track footer cards]

    //#region [Modal]
    const modal = document.querySelector("#modal_example") as HTMLElement;
    const btn = document.querySelector(".hero__button") as HTMLElement;
    const closeButtons = document.querySelectorAll(
      ".close, .modal__close-button",
    ) as NodeListOf<HTMLElement>;
    const body = document.querySelector("body") as HTMLElement;

    if (modal && btn && closeButtons) {
      // Set to true when modal opens
      function openModal() {
        modal.style.display = "block";
        body.style.overflow = "hidden";
      }

      // Set to false when modal closes
      function closeModal() {
        modal.style.display = "none";
        body.style.overflow = "";
      }

      btn.onclick = function () {
        openModal();
      };

      closeButtons.forEach((button) => (button.onclick = closeModal));

      window.onclick = function (event) {
        if (event.target == modal) {
          closeModal();
        }
      };

      // Close modal on esc key
      document.addEventListener("keydown", function (event) {
        if (event.key === "Escape") {
          closeModal();
        }
      });
    }

    //#endregion [Modal]
  });
</script>

<style>
  #hero {
    overflow: visible;
  }

  .hero {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .hero__logo {
    max-height: 256px;
    margin-bottom: 16px;
  }

  .hero__button {
    margin-top: 24px;
  }

  .example__info {
    margin: 0;
    max-width: 490px;
  }

  .example__info-list {
    padding: 0;
  }

  .example__info-list > * + * {
    margin-top: 16px;
  }

  .example__info > * + * {
    margin-top: 24px;
  }

  @media screen and (max-width: 768px) {
    .container.is--academy-selector {
      padding: unset;
    }
  }

  #rating {
    margin-bottom: 88px;
  }

  /*#region [Modal]*/
  .modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 10000; /* Sit on top */
    padding-top: 100px;
    padding-bottom: 56px;
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0, 0, 0); /* Fallback color */
    background-color: rgba(0, 0, 0, 0.4); /* Black w/ opacity */
  }

  /* Modal Content */
  .modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 24px;
    padding-top: 32px;
    border: 1px solid #888;
    width: 80%;
    border-radius: 16px;
  }

  @media screen and (max-width: 688px) {
    .modal-content {
      padding: 16px;
      padding-bottom: 24px;
      width: 90%;
    }
  }

  /* The Close Button */
  .close {
    color: #aaaaaa;
    float: right;
    font-size: 48px;
    font-weight: bold;
  }

  .close:hover,
  .close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
  }

  .modal__close-button {
    margin-top: 24px;
  }

  @media screen and (min-width: 688px) {
    .modal__close-button {
      margin-left: auto;
    }
  }

  /*#endregion [Modal]*/
</style>
