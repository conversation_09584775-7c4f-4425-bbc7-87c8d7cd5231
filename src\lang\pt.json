{"about": {"investors_cite": "A digitalização é inevitável para muitos setores, e o esporte não é exceção", "investors_cite_title": "diretora do setor de investimentos da Brayne", "investors_title": "Patrocinadores", "media_title": "Meios de comunicação", "partners_title": "<PERSON><PERSON><PERSON> parceiros", "subtitle": "Fundada em 2020 por um grupo de empreendedores e engenheiros de TI", "subtitle_2": "", "text": "Estamos desenvolvendo novas tecnologias para celulares usando a visão computacional e inteligência artificial e estamos trabalhando com a comunidade do futebol para criar um hub digital para jovens jogadores de futebol, técnicos, olheiros e times no mundo inteiro. Usando só a câmera do celular, nossos aplicativos - JuniStat & JuniCoach - ajudam a analisar a preparação física e técnica dos jogadores, acompanhar o seu desenvolvimento e receber recomendações para o melhor progresso.{br}{br}Com essa tecnologia esportiva, estamos criando o TID (sistema de identificação de talentos), onde os jovens jogadores têm perfis digitais e classificações de acordo com desenvolvimento deles. Este sistema analisa e depois oferece os dados estatísticas imparciais dos jovens jogadores no mundo inteiro e aumenta bastante as chances dos jogadores serem notados pelos times e olheiros sem qualquer obstaculo, mas só visualizando o talento e motivação deles.", "title": "JuniStat{copyright} é uma empresa de inteligência artificial"}, "clubs": {"app_coach": "Aplicativo para o técnico", "connect_club": "Conectar o time", "create_club": "Cadastrar o time", "exercises": {"title": "14 testes digitais disponíveis no aplicativo"}, "exercises_subtitle": "Esses testes funcionam no aplicativo para facilitar o trabalho do técnico, olheiros e jogadores. Cada teste mostra a métrica do jogador e os dados intermediários na sua faixa etária", "exercises_title": "Mais de 15 testes", "features": {"card": {"description": "Perfil da marca com o logotipo do clube", "title": "Cartão digital interativo"}, "data": {"card_title": "Sobre o jogador", "description_text": "Jogador de futebol com paixão pelo esporte e vontade de vencer. Alex é um atacante com um trabalho de pés extremamente rápido, excelente controle de bola e um olhar atento para o gol. Ele tem um talento natural para ler o jogo e tomar decisões em frações de segundo que muitas vezes resultam em oportunidades de gol para sua equipe.", "height": "Altura, cm", "position": "Posição", "position_value": "CM, LCM", "title": "Dad<PERSON> adiciona<PERSON>", "weight": "Peso, kg"}, "results": {"title": "Resultados dos testes"}, "title": "De<PERSON><PERSON> de testar, cada jogador recebe", "video": {"title": "Melhores videoclipes do jogador"}}, "features_card_text1": "Cada jogador recebe um cartão digital e um perfil de marca", "features_card_text2": "Todos os dados do jogador são protegidos e armazenados de acordo com os padrões de GDPR, ISO e país industrial. Cada jogador está ligado ao seu representante adulto", "features_card_text3": "O clube faz com que os jogadores sejam fechados ou abertos para visualização a seu critério", "guide_title": "Manual passo a passo para a plataforma Juni", "hero": {"points": [{"they": "Láseres", "we": "Fácil de usar e de baixo custo"}, {"they": "Observações subjetivas", "we": "Estatísticas objetivas AI/ML"}, {"they": "Análise tradicional", "we": "Mais de 65 parâmetros de <br />desempenho físico e técnico"}], "reg_button": "Criar uma conta", "reg_description": "Obtenha 15 testes para uma avaliação gratuita", "subtitle": "O sistema de teste inteligente ajuda academias e clubes a coletar mais de 65 pontos de dados para identificar talentos e compreender o desenvolvimento dos jovens.", "title": "Teste, monitoramento e avaliação comparativa das habilidades dos jogadores com o aplicativo JuniCoach", "form": {"name_input": "Nome completo", "email_input": "Email", "phone_input": "Telefone", "academy_input": "Nome da academia", "send_button": "Solicitar apresentação", "legal_text": "Ao clicar no botão, você concorda com a ", "legal_text_privacy": "política de privacidade", "form_error": "Lamentamos! Parece que sua solicitação não foi enviada. Por favor, tente enviar novamente mais tarde. Se o problema persistir, entre em contato <NAME_EMAIL>.", "form_success": "Obrigado! Prepararemos a apresentação para você em breve e a enviaremos para o email especificado."}}, "how": {"equip": "Seu clube já tem o equipamento necessário", "equip_item": {"cones": "Marcadores", "phone": "Smartphone", "tape": "<PERSON>ta métrica", "tripod": "<PERSON><PERSON>"}, "free_trial": "<PERSON><PERSON> de teste gratuito", "point1": {"title": "O treinador grava os vídeos seguindo as diretrizes do aplicativo"}, "point2": {"title": "O programa, baseado em inteligência artificial, os processa imediatamente e fornece métricas precisas dos jogadores."}, "title": "Como funciona"}, "whitelabel": {"page_title": "Soluções de marca branca para testes de jogadores de futebol com IA", "hero": {"badge": "100+ clubes e academias", "title": "Soluções de marca branca para testes de jogadores de futebol com IA", "description": "Sua própria seção com marca personalizada com uma bateria de testes no aplicativo JuniCoach, com o logotipo e cores do seu clube ao pedir a partir de 10.000 testes"}, "features": {"access": "Acesso para treinadores e olheiros aos resultados na conta pessoal e aplicativo móvel", "pricing": "Testes a preços especiais para contratos a partir de 2 milhões ₽", "custom_tests": "Possibilidade de desenvolver testes de acordo com os requisitos do clube"}, "technology": {"title": "Tecnologia avançada de testes e scouting nas cores do clube", "description": "Usando apenas telefones celulares e tripés, os treinadores podem testar rapidamente e obter dados precisos dos jogadores, compará-los com padrões, identificar e desenvolver talentos", "recommendation": "Verificado por especialistas da RFS e usado para avaliação abrangente de jogadores", "recommendation_link": "Carta de recomendação ↗"}, "equipment": {"title": "Testes sem equipamentos caros, em qualquer lugar", "process1": "O treinador grava víde<PERSON> seguindo as instruções do aplicativo", "process2": "A plataforma processa instantaneamente os dados e fornece métricas precisas dos jogadores"}, "tests": {"title": "Testes sem equipamentos caros, em qualquer lugar"}, "modern": {"title": "Abordagem moderna para seleção e desenvolvimento de jogadores", "list1": "Compare jogadores com colegas de todo o mundo", "list2": "Compartilhe perfis com pais", "list3": "Confirme as decisões dos treinadores com dados objetivos", "security": "Segurança e proteção de dados sob controle rigoroso"}, "media": {"title": "Mídia sobre nós", "article1": "Freedom QJ League realizou testes digitais de jogadores", "article2": "Programa de digitalização para estudantes do Zenit-Championika", "article3": "Seleção de jogadores para festival usando testes inteligentes"}, "cta": {"title": "Conecte sua marca no JuniCoach", "contact_manager": "Con<PERSON><PERSON> gerente", "instructions": "Instruções de operação do sistema"}, "modal": {"title": "Con<PERSON><PERSON> gerente", "close": "<PERSON><PERSON><PERSON> janela modal", "name_label": "Nome *", "name_placeholder": "Digite seu nome", "email_label": "Email *", "email_placeholder": "<EMAIL>", "phone_label": "Telefone *", "phone_placeholder": "+55 (99) 9123-4567", "academy_label": "Nome da academia/clube *", "academy_placeholder": "Nome da sua academia ou clube", "submit": "Enviar solicitação", "submitting": "Enviando...", "legal": "Ao clicar no botão, você concorda com a", "privacy_link": "política de privacidade", "success_title": "Solicitação enviada!", "success_message": "Entraremos em contato com você em breve", "error_title": "Erro de envio", "error_message": "Tente novamente ou entre em contato conosco diretamente", "retry": "Tentar novamente"}, "footer": "JuniStat LLC © Moscou"}, "player_example_title": "O exemplo de cartão de jogador", "player_open_profile": "<PERSON><PERSON><PERSON> perfil", "profile_protect_text": "Todos os dados do jogador são protegidos e armazenados de acordo com os padrões de GDPR, ISO e país industrial. Cada jogador está ligado ao seu representante adulto", "quote": "Os testes sistemáticos aumentam bastante o nível de aprendizagem dos jovens jogadores e ajudam a desenvolver as habilidades básicas de futebol", "recommends": {"ian_text": "“A tecnologia mede atributos técnicos e físicos dos jogadores que são cientificamente testados para afetar o desempenho no futebol. Academy can now quickly test players using a smartphone and tripod, upload this data soon to a digital hub and drill deeper into an individual player’s performance levels. <br /> Data is now driving the entire talent ID and development process, and the Academy can access data quickly to make any required changes to a player’s individual training plan. <br /> When evaluating players for opportunities in Europe, the Academies strive to measure potential versus current performance. Portanto, é essencial acompanhar a taxa de desenvolvimento dos jovens jogadores ao longo do tempo e a consistência com que eles melhoram.”", "show_less": "<PERSON><PERSON> menos", "show_more": "<PERSON><PERSON> mais"}, "recommends_title": "Somos recomendados por", "rendered_subtitle": "permite aos times identificar os jogadores talentosos mais rápido e mais eficiente e fazer a avaliação digital", "rendered_title": "O Sistema dos testes digitais", "step_text1": "Registar o time. De<PERSON>is da aprovação terá acesso ao sistema", "step_text2": "Colocar os dados dos técnicos e jogadores na conta pessoal do time", "step_text3": "Entre em contato conosco para garantir a data do teste do aplicativo . Teremos o maior prazer em ensinar seus técnicos a trabalhar com o Sistema", "steps": {"text1": "Monitorar e comparar os dados dos jogadores com os de seus pares em todo o mundo", "text2": "Analise os dados com suas ferramentas ou use nossos insights", "text3": "Procurar e selecionar jogadores para os testes off-line", "title": "Registre-se e explore as funções da plataforma em seu painel pessoal na Web."}, "title": "JuniCoach{copyright} ajuda a testar os jogadores mais rápido usando os resultados dos treinos digitais regulares", "trial_testing": "Teste do aplicativo"}, "faq": {"feedback": "Esta resposta o ajudou?"}, "faq_list": [{"answer": "Normalmente, os resultados do teste são devolvidos em 1 dia. Às vezes, pode levar até 5 dias para processar um teste. Siga as instruções ao fazer o teste para reduzir a chance de rejeição.", "title": "Quanto tempo leva para processar o teste? Quando receberei os resultados?"}, {"answer": "Existem vá<PERSON><PERSON> maneiras:<br /><ul><li>Enviar um convite para se juntar a um representante no aplicativo do player</li><li>Cadastrar-se como representante - <a href=\"https ://aplicativo. junistat.com/sign-up?role=mentor\">app.junistat.com/sign-up?role=mentor</a> e envie um convite ao jogador</li><li>Se o jogador estiver registrado através da academia, peça ao funcionário da academia para adicioná-lo como representante</li></ul>", "title": "Como conectar um representante a um jogador?"}, {"answer": "Você só pode pagar pela assinatura na conta do representante.<br /><ol><li>Faça login na conta do representante <a href=\"https://app.junistat.com/login? role=mentor\">app.junistat.com/login?role=mentor</a></li><li>Selecione o jogador que deseja assinar</li><li>Clique em \"Inscrever-se\"</li></ol> ", "title": "Como pagar por uma assinatura?"}, {"answer": "Você pode acompanhar os resultados do jogador em sua conta de representante - <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role= mentor</a> ou na classificação geral no site se o perfil do jogador estiver aberto — <a href=\"https://junistat.com/\">junistat.com</a>", "title": "Como acompanhar os resultados do seu filho?"}, {"answer": "<ul><li>Os resultados tornam-se menos relevantes com o tempo. Faça testes regularmente para manter seu máximo classificação. </li> <li>As pontuações médias dos testes estão melhorando. Novos jogadores com desempenho melhor do que o seu estão aparecendo e sua classificação é reduzida de acordo.</li> <li>As pontuações dos jogadores pioraram.</li></ul>", "title": "Por que a classificação está diminuindo?"}, {"answer": "<ol><li><PERSON>aça login na conta do representante <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor </a></li><li>Selecione o player para o qual deseja adicionar um vídeo ou descrição.</li><li>Selecione a guia \"Sobre\" no menu</li><li> Adicione uma descrição ou envie um vídeo</li><li>Clique em \"Salvar\"</li></ol>", "title": "Como adiciono um vídeo e uma descrição do player?"}, {"answer": "<ol><li><PERSON>aça login na conta do representante <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor </a></li><li>Clique no botão \"Mesclar perfis\"</li><li>Selecione os perfis dos jogadores da academia e do aplicativo JuniStat</li></ol>", "title": "Como mesclar perfis de jogadores? 2 jogadores idênticos na conta rep, como deixar um?"}, {"answer": "A assinatura dá acesso a todos os testes. Possibilidade de disponibilizar o jogador para visualização por academias e clubes.", "title": "Para que serve a assinatura?"}, {"answer": "Você pode se registrar no aplicativo JuniStat, fazer testes e talvez um dos olheiros, academias, clubes notará você e o convidará para sua casa.", "title": "Como faço para assistir clubes e academias? Como faço para ingressar em uma academia?"}, {"answer": "<ol><li>Fa<PERSON> login na conta do representante <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor </a></li><li>Selecionar jogador</li><li>Na página do jogador selecionado, vá para a seção \"Configurações\"</li><li>No final da página , clique no botão \"Desconectar jugador\"</li></ol>", "title": "Como desvincular um representante de um jogador?"}, {"answer": "Envie-nos um e-mail <a href=\"mailto:<EMAIL>\"><EMAIL></a>. Digite o nome, número de telefone ou e-mail do jogador.", "title": "Como deletar uma conta?"}, {"answer": "O jogador não precisa ter um representante para fazer isso. Peça ao representante atual para desvincular o jogador de seu perfil.", "sub_answers": [{"answer": "<h4>Enviando um e-mail para o representante</h4><ol><li>Faça login na conta da academia <a href=\"https://app.junistat.com/login?role=academy\">app.junistat.com/login?role=academy</a></li><li>Na lista de jogadores, clique em \"Convidar um representante\"</li><li>Digite o e-mail do representante e clique \"Enviar solicitação\"</li></ol><br /><h4>Compartilhar perfil</h4><ol><li>Fazer login na Academy <a href=\"https://app.junistat.com/login?role=academy\">href=\"https://app.junistat. com/login?role=academy\"> app.junistat.com/login?role=academy</a></li><li>Na lista de jogadores ou na página do jogador, clique em \"Compartilhar\"</li><li>Envie o link para o representante de qualquer forma.</li> <li>O representante deve se registrar usando este link.</li></ol>", "title": "No escritório da academia"}, {"answer": "<ol><li>A<PERSON>r a lista de jogadores</li> <li>Em na frente do player, clique no ícone \"Convidar representante\"</li> <li>Especifique o endereço de e-mail do representante e clique em \"Enviar\"</li></ol>", "title": "No aplicativo JuniCoach"}], "title": "Como enviar um convite a um representante?", "type": "academy"}, {"answer": "Entre em contato com o representante atual para desvincular o jogador de seu perfil. Quando o jogador estiver livre do representante, você pode convidar um novo.", "title": "Como substituir o representante do jogador?", "type": "academy"}], "meta": {"description": "Aplicações inteligentes para jovens futebolistas, treinadores e clubes. Classificações internacionais, provas, identificação de talentos e scouting"}, "navigation": {"about": "Sobre nós", "clubs": "Times, Técnicos, Olheiros", "faq": "FAQ", "parents": "Pais e representantes legais", "press_kit": "<PERSON>", "privacy": "Política de privacidade", "rating": "Classificação", "rating_system": "Sistema de classificação", "scouting": "Digital scouting", "sponsors": "Patrocinadores", "team": "Equipe"}, "parents": {"about_cards": "Um cartão pessoal, um perfil digital e uma classificação na base de dados internacional entre os jovens jogadores de futebol", "about_skills_subtitle": "A possibilidade de competir e comparar-se com os outros jogadores do mundo inteiro", "about_skills_title": "Uma avaliação objetiva das habilidades do jogador de futebol e recomendações para o desenvolvimento", "app_for_player": "O aplicativo para o jogador", "chart_text": "O perfil digital inclui todos os dados da preparação física e técnica do jovem jogador de futebol, o histórico e os trechos de vídeos com jogos dele. Dependendo da classificação e das metas alcançadas o jogador pode receber um convite para fazer o seu teste em alguns times", "create_parent_profile": "Criar um perfil dos pais ou do representante legal", "title": "JuniStat{copyright} descobre novos talentos de futebol"}, "pricing": {"from_3000": "from 3,000 players", "from_3000_price_month": "{currency}4/month", "from_3000_price_year": "{currency}40/year", "license": {"point1": "Unlimited access to tests in JuniStat and JuniCoach mobile apps", "point2": "Digital cards and player profiles with an objective evaluation skills", "point3": "Dynamics of development, players' benchmarking, and record of progress (digital track", "point4": "Player ratings in the international database of young football players", "point5": "Development tips, insights, and recommendations for players and coaches", "point6": "A personal web account of the Academy with statistics, analytics, videos, selection block, and reports", "title": "License includes"}, "price_player": "Price per player", "price_player_description": "1 player = 1 license", "title": "Plans for Business", "up_to_1000": "up to 1 000 players", "up_to_1000_price_month": "{currency}5/month", "up_to_1000_price_year": "{currency}50/year", "up_to_3000": "from 1,000 to 3,000 players", "up_to_3000_price_month": "{currency}4.5/month", "up_to_3000_price_year": "{currency}45/year"}, "rating": {"banner": {"subtitle": "Comece a testar baixando o aplicativo", "title": "Receba a sua classificação"}, "card_clubs": "Solução para times", "card_parents": "Pais e representantes legais", "example": {"point1": "Cartão pessoal com avaliação imparcial de habilidades", "point2": "Perfil digital com dados de desempenho objetivos e destaques", "point3": "Classificação na base internacional de talentos", "point4": "Chance de ser observado online", "title": "Cada jogador recebe"}, "filters": "<PERSON><PERSON><PERSON>", "grid": {"error": "Erro ao carregar jogadores. Atualize a página ou tente novamente mais tarde", "next": "Próximo", "prev": "Anterior", "empty": "Jogadores não encontrados ou escondidos"}, "popup": {"button_login": "<PERSON><PERSON><PERSON>", "button_signUp": "Inscrever-se", "description": "Para obter acesso total ao banco de dados do jogador e pesquisa avançada", "title": "Crie sua conta"}, "search_placeholder": "Nome do jogador", "select_club": "Selecione um clube", "titleAllPlayers": "Todos os jogadores"}, "sign_up": "Cadastre-se", "Top 5 Startups 2025": "Top 5 Startups 2025", "sponsors": {"contact": "Sinta-se livre para contatar - <PERSON><PERSON><PERSON>", "text": "Esse Sistema usam mais de 100 mil jovens jogadores de futebol no mundo inteiro.Novos talentos fazem registros diariamente. Mas nem todos tem recursos financeiros para arcar com a mensalidade.Os patrocinadores poderão ajudar esses jogadores a pagar suas mensalidades.Nos cartões do jogador vai aparecer o logotipo do patrocinador durante todo o tempo da assinatura. Estão disponíveis os Pacotes a partir de 100 assinaturas.", "title": "Ajude os jovens jogadores a terem a chance de entrar no mundo de futebol!"}, "team": {"ambassador_title": "Embaixadores", "how_become_ambassador_text": "Caso ter vontade de representar a JuniStat no seu país, entre em contato conosco —", "how_become_ambassador_title": "Como se tornar o nosso Embaixador?", "team_title": "Gestão e operação"}}