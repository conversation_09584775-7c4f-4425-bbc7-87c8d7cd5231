import { LokaliseApi } from "@lokalise/node-api";
import fs from "fs";

const apiKey = "4c115b3565d755cd78785218bc3fd64dc7809fa7";
const project_id = "138866886417552d55c261.90303764";
const langCode = "en";
const filePath = `src/lang/${langCode}.json`;

// Read the file as a buffer
const fileBuffer = fs.readFileSync(filePath);

// Convert the buffer to base64
const base64String = fileBuffer.toString("base64");

const lokaliseApi = new LokaliseApi({ apiKey });
lokaliseApi
  .files()
  .upload(project_id, {
    data: base64String,
    lang_iso: langCode,
    filename: "%LANG_ISO%.json",
    replace_modified: true,
    detect_icu_plurals: false,
    convert_placeholders: false,
  })
  .then((data) => {
    console.log(data);
  })
  .catch((err) => {
    console.log("err :>> ", err);
  });
