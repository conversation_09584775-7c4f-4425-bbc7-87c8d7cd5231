@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Hero]*/
.hero {
  margin-top: $space-48;
}

.hero__title {
  width: 90%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.hero__subtitle {
  max-width: 46rem;
  margin-top: $space-24;
}

.hero__copy {
  margin-top: $space-48;
  max-width: 43.75rem;
  line-height: 1.35em;
}

.hero__email-link-wrap {
  display: inline-flex;
  align-items: center;
}

.hero__copy-email {
  display: none;

  @media screen and (min-width: $desktop) {
    @include transition-colors;

    display: inline-block;
    &:hover {
      color: $blue;
    }
  }
}

/*#endregion [Hero]*/

/*#region [Ambassadors]*/
.ambassadors__title {
  margin-bottom: $space-48;
}
/*#endregion [Ambassadors]*/

/*#region [Management]*/
.management__title {
  margin-bottom: $space-48;
}
/*#endregion [Management]*/

/*#region [Cards]*/
.team__cards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $space-64;

  @media screen and (max-width: $tablet) {
    grid-template-columns: repeat(3, 1fr);
    gap: $space-48;
  }

  @media screen and (max-width: $mobile) {
    grid-template-columns: repeat(2, 1fr);
    gap: $space-24;
  }
}

/*#endregion [Cards]*/
