---
interface Props {
  class?: string;
  arrow?: boolean;
  id?: string;
}

const { class: calssList, arrow, id } = Astro.props;
---

<a
  id={id}
  class:list={["button", calssList]}
  href="https://app.junistat.com/sign-up?role=academy"
  target="_blank"
>
  <div>
    <p class="button-description">
      <slot name="subtitle">+25 тестов бесплатно</slot>
    </p>
    <h3 class="button-label">
      <slot name="label">Зарегистрировать академию</slot>
    </h3>
  </div>
  {
    arrow && (
      <svg
        class="button-arrow"
        xmlns="http://www.w3.org/2000/svg"
        width="48"
        height="48"
        fill="none"
        viewBox="0 0 48 48"
      >
        <path
          fill="#292F32"
          d="m44.561 25.06-18 18A1.5 1.5 0 0 1 24 42v-7.5H9a3 3 0 0 1-3-3v-15a3 3 0 0 1 3-3h15V6a1.5 1.5 0 0 1 2.561-1.062l18 18a1.5 1.5 0 0 1 0 2.123Z"
        />
      </svg>
    )
  }
</a>

<style>
  .button {
    width: fit-content;
    justify-content: space-between;
    background-color: var(--primary);
    padding: 16px 24px;
    gap: 24px;

    border-radius: 12px;
    background: #ffe83f;
    box-shadow:
      0px 1px 2px 0px rgba(171, 168, 79, 0.14),
      0px 3px 3px 0px rgba(171, 168, 79, 0.12),
      0px 7px 4px 0px rgba(171, 168, 79, 0.07),
      0px 13px 5px 0px rgba(171, 168, 79, 0.02),
      0px 20px 6px 0px rgba(171, 168, 79, 0);

    transition-property: background-color, box-shadow;
    transition-duration: 0.3s;
    transition-timing-function: var(--smooth-ease);
  }

  .button-arrow {
    width: 48px;
  }

  @media screen and (any-hover: hover) {
    .button:hover {
      background: #ffed63;
      box-shadow: 0px 1px 2px 0px rgba(171, 168, 79, 0.14);
    }
  }
</style>
