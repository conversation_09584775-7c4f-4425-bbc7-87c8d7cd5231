---
import { getCollection } from "astro:content";
import BlogLayout from "@layouts/BlogLayout.astro";

export async function getStaticPaths() {
  const blogEntries = await getCollection("blog-ru");
  return blogEntries.map((entry) =>
    // console.log("entry :>> ", entry),
    ({
      params: { slug: entry.slug },
      props: { entry },
    }),
  );
}
const { entry } = Astro.props;

const { Content } = await entry.render();
---

<BlogLayout frontmatter={entry.data}>
  <Content />
</BlogLayout>
