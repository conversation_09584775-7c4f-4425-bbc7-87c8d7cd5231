---
import { Image } from "astro:assets";
import AppStoreButton from "@assets/images/app-store.svg";
import GooglePlayButton from "@assets/images/google-play.svg";

interface Props {
  app: "coach" | "player";
  class?: string;
  place?: string;
}

const { app, class: classList, place } = Astro.props;

const androidApp =
  app === "coach"
    ? "https://play.google.com/store/apps/details?id=com.junistat.coach"
    : "https://play.google.com/store/apps/details?id=com.junistat.player";

const iphoneApp =
  app === "coach"
    ? "https://apps.apple.com/us/app/junicoach/id1597370545"
    : "https://apps.apple.com/us/app/junistat-football-hub/id1554737071";
---

<div class:list={["download-buttons", classList]}>
  <a
    class="download-buttons__button"
    href={iphoneApp}
    data-app-platform="IOS"
    data-place={place ?? ""}
    target="_blank"
  >
    <Image src={AppStoreButton} alt="Download on the App Store" />
  </a>
  <a
    class="download-buttons__button"
    href={androidApp}
    data-app-platform="Android"
    data-place={place ?? ""}
    target="_blank"
  >
    <Image src={GooglePlayButton} alt="Get it on Google Play" />
  </a>
</div>

<script>
  import { track } from "@amplitude/analytics-browser";

  const donwloadButtons = document.querySelectorAll(
    ".download-buttons__button"
  ) as NodeListOf<HTMLLinkElement>;

  donwloadButtons.forEach((button) => {
    button.addEventListener("click", () => {
      const platform = button.dataset.appPlatform;
      const place = button.dataset.place;

      if (platform) {
        track("Click download button", {
          platform: platform,
          place: place,
        });
      }
    });
  });
</script>

<style lang="scss">
  @use "@styles/_variables.scss" as v;

  .download-buttons {
    display: flex;
    column-gap: v.$space-16;
    margin-top: v.$space-32;
    // flex-wrap: wrap;
  }

  .download-buttons__button {
    height: 60px;

    & img {
      transition-property: transform, opacity;
      transition-duration: 0.4s;
      animation-duration: 0.4s;
      transition-timing-function: ease-in-out;
    }

    &:hover img {
      animation-name: zoom;
      opacity: 0.6;
    }
  }

  img {
    height: 100%;
  }

  @media screen and (max-width: 768px) {
    .download-buttons img {
      height: 45px;
    }
  }

  @keyframes zoom {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(0.95);
    }

    100% {
      transform: scale(1);
    }
  }
</style>
