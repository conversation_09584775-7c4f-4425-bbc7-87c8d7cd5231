import { defineConfig } from "astro/config";
import svelte from "@astrojs/svelte";
import { vitePreprocess } from "@astrojs/svelte";
import compress from "astro-compress";
import compressor from "astro-compressor";
import mdx from "@astrojs/mdx";

import sitemap from "@astrojs/sitemap";

import tailwindcss from "@tailwindcss/vite";
import Icons from 'unplugin-icons/vite'

// https://astro.build/config
export default defineConfig({
  site: "https://junistat.com",
  scopedStyleStrategy: "where",

  integrations: [
    svelte(
      {
        preprocess: [vitePreprocess()],
      },
      mdx(),
    ),
    compress({
      Image: false,
      SVG: false,
    }),
    compressor(),
    mdx(),
    sitemap({
      filter: (page) => page !== "https://junistat.com/all-pages/",
      i18n: {
        defaultLocale: "en", // All urls that don't contain `es` or `fr` after `https://stargazers.club/` will be treated as default locale, i.e. `en`
        locales: {
          en: "en-US", // The `defaultLocale` value must present in `locales` keys
          ru: "ru-RU",
          es: "es-ES",
          pt: "pt-PT",
        },
      },
    }),
  ],

  vite: {
    plugins: [tailwindcss(), Icons({
      compiler: 'astro',
    }),],
  },
});