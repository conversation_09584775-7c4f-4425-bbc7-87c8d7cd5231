---
import "@styles/main.scss";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

//#endregion [i18n]
import { ui } from "@lang/langs";

const faqContent = (nested?: boolean): any => {
  for (const [key, value] of Object.entries(ui)) {
    if (key === lang) {
      return (value as { faq_list: any })["faq_list"];
    }
  }
};

let playersQuestions: Array<[]> = [];
let academyQuestions: Array<[]> = [];

faqContent().filter((question: any) =>
  question.type
    ? academyQuestions.push(question)
    : playersQuestions.push(question)
);

//#region [Styles]
import "@styles/pages/faq.scss";
//#endregion [Styles]

//#region [Built-in components]
import { Image } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import FaqItem from "@components/FaqItem.astro";
//#endregion [Components]
---

<meta charset="utf-8" />
<Layout>
  <div id="faq" class="section">
    <div class="container">
      <div class="faq">
        {
          playersQuestions.map((question) => {
            return <FaqItem question={question} />;
          })
        }
        <br />
        <h1 class="h2">Academy</h1>
        {
          academyQuestions.map((question) => {
            return <FaqItem question={question} />;
          })
        }
      </div>
    </div>
  </div>
</Layout>

<script></script>
