---
import Layout from "@layouts/Layout.astro";
import { Image } from "astro:assets";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
//#endregion [i18n]

// Components
import CTA from "@components/whitelabel/CTA.astro";
import TestsList from "@components/whitelabel/TestsList.astro";

// Images
import JuniCoachLogo from "@assets/images/junicoach-logo.svg";

import HeroImgRu from "@assets/images/whitelabel/whitelabel-hero-ru.png";
import HeroImgEn from "@assets/images/whitelabel/whitelabel-hero-en.png";

import JunicoachRecordRu from "@assets/images/whitelabel/junicoach-record-ru.png";
import JunicoachResultTestRu from "@assets/images/whitelabel/junicoach-result-test-ru.png";
import JunicoachRecordEn from "@assets/images/whitelabel/junicoach-record-en.png";
import JunicoachResultTestEn from "@assets/images/whitelabel/junicoach-result-test-en.png";

import PlayersCardsRu from "@assets/images/whitelabel/whitelabel-players-cards-ru.png";
import PlayersCardsEn from "@assets/images/whitelabel/whitelabel-players-cards-en.png";

// How
import ConesImg from "@assets/images/whitelabel/how/cones.jpg";
import PhoneImg from "@assets/images/whitelabel/how/phone.jpg";
import TapeImg from "@assets/images/whitelabel/how/tape.jpg";
import TripodImg from "@assets/images/whitelabel/how/tripod.jpg";
import EquipPhotoImg from "@assets/images/whitelabel/how/equip-photo.jpg";

// Icons
import SolarTShirtBold from "~icons/solar/t-shirt-bold";
import SolarDevicesBoldDuotone from "~icons/solar/devices-bold-duotone";
import SolarCrownMinimalisticBoldDuotone from "~icons/solar/crown-minimalistic-bold-duotone";
import SolarCode2BoldDuotone from "~icons/solar/code-2-bold-duotone";
import SolarVerifiedCheckBold from "~icons/solar/verified-check-bold";
import SolarVideocameraAddBold from "~icons/solar/videocamera-add-bold";
import SolarChartBold from "~icons/solar/chart-bold";
import SolarShieldCheckBold from "~icons/solar/shield-check-bold";
import SolarCalendarAddBold from "~icons/solar/calendar-add-bold";
---

<Layout title={t("whitelabel.page_title")} contentOnly autoChangeLang={false}>
  <!-- Hero -->
  <section class="">
    <header class="mt-8">
      <div class="container">
        <Image src={JuniCoachLogo} alt="JuniCoach" class="w-auto h-9" />
      </div>
    </header>

    <div class="z-10 relative mt-8 max-lg:mt-10 container">
      <div
        class="flex max-md:flex-col justify-between md:items-center md:gap-4"
      >
        <div class="flex flex-col items-start max-w-screen-lg lg:max-w-[40rem]">
          <!-- Badge -->
          <div
            class="flex items-center gap-2 bg-bg-accent px-3 py-2 border border-white rounded-full font-medium text-sm"
          >
            <SolarTShirtBold class="size-6" />
            <span>{t("whitelabel.hero.badge")}</span>
          </div>

          <!-- Headline -->
          <h1 class="mt-4 font-bold text-3xl md:text-5xl xl:text-6xl">
            {t("whitelabel.hero.title")}
          </h1>
          <p class="mt-4 lg:mt-8 text-base lg:text-xl xl:text-2xl">
            {t("whitelabel.hero.description")}
          </p>

          <!-- CTA -->
          <CTA class="mt-8 lg:mt-14" />
        </div>

        <Image
          src={lang === "ru" ? HeroImgRu : HeroImgEn}
          alt=""
          class="max-md:mt-10 max-w-[30.75rem] xl:max-w-[36.75rem] object-contain"
        />
      </div>
    </div>
  </section>

  <!-- Cards -->
  <section class="mt-14">
    <div class="container">
      <div class="flex flex-col gap-6 lg:grid lg:grid-cols-[1fr_0.7fr_0.7fr]">
        {
          [
            {
              text: t("whitelabel.features.access"),
              icon: SolarDevicesBoldDuotone,
            },
            {
              text: t("whitelabel.features.pricing"),
              icon: SolarCrownMinimalisticBoldDuotone,
            },
            {
              text: t("whitelabel.features.custom_tests"),
              icon: SolarCode2BoldDuotone,
            },
          ].map((card) => {
            return (
              <div class="flex flex-col items-start gap-4 bg-white p-6 rounded-2xl w-full text-lg xl:text-2xl">
                <div class="p-4 rounded-2xl bg-accent-primary">
                  <card.icon class="size-10 lg:size-16" />
                </div>
                <p>{card.text}</p>
              </div>
            );
          })
        }
      </div>
    </div>
  </section>

  <!-- Technology -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <div
        class="flex flex-col gap-6 lg:grid lg:grid-cols-[1fr_0.9fr] lg:grid-rows-auto"
      >
        <!-- Content cell -->
        <div class="items-start max-lg:gap-4 max-lg:grid md:grid-cols-2">
          <div>
            <h2 class="font-bold text-2xl/tight lg:text-5xl">
              {t("whitelabel.technology.title")}
            </h2>
            <p class="mt-6 lg:mt-8 text-base lg:text-xl xl:text-2xl">
              {t("whitelabel.technology.description")}
            </p>
          </div>
          <!-- Recommend -->
          <div
            class="flex max-lg:flex-col lg:items-center gap-4 lg:mt-10 p-4 rounded-2xl text-white bg-accent-success"
          >
            <SolarVerifiedCheckBold class="size-16 lg:size-32" />
            <div>
              <div class="font-medium text-lg/tight lg:text-xl xl:text-2xl">
                {t("whitelabel.technology.recommendation")}
              </div>

              {
                lang === "ru" && (
                  <a
                    href="https://junistat.com/_astro/rfs-ru.c3QLmpSn.jpg"
                    target="_blank"
                    class="block mt-2 underline hover:no-underline"
                  >
                    {t("whitelabel.technology.recommendation_link")}
                  </a>
                )
              }
            </div>
          </div>
        </div>

        <!-- Images -->
        <Image
          src={lang === "ru" ? JunicoachResultTestRu : JunicoachResultTestEn}
          alt=""
          class="max-lg:hidden row-span-2 lg:max-w-[32rem]"
        />
        <Image
          src={lang === "ru" ? JunicoachRecordRu : JunicoachRecordEn}
          alt=""
          class="max-md:w-[150vw] max-w-full max-md:max-w-none"
        />
      </div>
    </div>
  </section>

  <!-- Equipment -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <h2 class="max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        {t("whitelabel.equipment.title")}
      </h2>
      <div class="gap-6 grid md:grid-cols-2 mt-12">
        <div
          class="flex max-md:flex-col items-start md:items-center gap-4 bg-white p-6 rounded-2xl w-full text-lg/tight lg:text-2xl"
        >
          <div class="p-4 rounded-2xl bg-accent-primary">
            <SolarVideocameraAddBold class="size-10 lg:size-16" />
          </div>
          <span>{t("whitelabel.equipment.process1")}</span>
        </div>
        <div
          class="flex max-md:flex-col items-start md:items-center gap-4 bg-white p-6 rounded-2xl w-full text-lg/tight lg:text-2xl"
        >
          <div class="p-4 rounded-2xl bg-accent-primary">
            <SolarChartBold class="size-10 lg:size-16" />
          </div>
          <span>{t("whitelabel.equipment.process2")}</span>
        </div>
      </div>
      <div class="gap-6 grid md:grid-cols-2 mt-6">
        <div
          class="max-lg:items-center gap-4 grid grid-cols-[auto_1fr] lg:grid-cols-2 grid-rows-2 max-lg:bg-white max-lg:p-4 max-lg:rounded-2xl"
        >
          {
            [
              {
                title: t("clubs.how.equip_item.phone"),
                img: PhoneImg,
              },
              {
                title: t("clubs.how.equip_item.tripod"),
                img: TripodImg,
              },
              {
                title: t("clubs.how.equip_item.cones"),
                img: ConesImg,
              },
              {
                title: t("clubs.how.equip_item.tape"),
                img: TapeImg,
              },
            ].map((equip) => {
              return (
                <div class="max-lg:contents flex max-lg:flex-col items-center lg:gap-4 lg:bg-white p-6 rounded-2xl w-full text-lg/tight">
                  <Image
                    src={equip.img}
                    alt={equip.title}
                    class="w-26 xl:w-32 h-auto"
                  />
                  <span class="">{equip.title}</span>
                </div>
              );
            })
          }
        </div>
        <CTA class="col-1" />
        <Image
          class="md:row-start-1 rounded-2xl w-full h-full object-cover md:col-2"
          src={EquipPhotoImg}
          alt="Coach tests Antalyaspor team via JuniCoach"
        />
      </div>
    </div>
  </section>

  <!-- Tests -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <h2 class="mb-12 max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        {t("whitelabel.tests.title")}
      </h2>
      <TestsList lang={lang} />
    </div>
  </section>

  <!-- Modern -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <div
        class="gap-8 grid md:grid-cols-2 bg-white p-4 lg:p-10 rounded-2xl w-full"
      >
        <!-- Col 1 -->
        <div class="flex flex-col gap-6">
          <h2 class="max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
            {t("whitelabel.modern.title")}
          </h2>
          <ul class="space-y-2 ml-6 text-base lg:text-2xl list-disc">
            {
              [
                t("whitelabel.modern.list1"),
                t("whitelabel.modern.list2"),
                t("whitelabel.modern.list3"),
              ].map((listItem) => {
                return (
                  <li class="">
                    <span>{listItem}</span>
                  </li>
                );
              })
            }
          </ul>
          <CTA />

          <!-- Secure badge -->
          <div
            class="flex justify-center items-center gap-4 bg-success-50 p-2 md:px-4 border border-success-500 rounded-full text-base/tight"
          >
            <SolarShieldCheckBold class="size-6 text-accent-success shrink-0" />
            {t("whitelabel.modern.security")}
          </div>
        </div>
        <!-- Col 2 -->
        <div
          class="flex justify-center items-center p-2 lg:p-6 rounded-2xl bg-accent-primary"
        >
          <Image
            src={lang === "ru" ? PlayersCardsRu : PlayersCardsEn}
            alt="Players cards"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Media about us -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <h2 class="max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        {t("whitelabel.media.title")}
      </h2>

      <div class="gap-4 grid sm:grid-cols-2 lg:grid-cols-3 mt-12">
        {
          [
            {
              title: t("whitelabel.media.article1"),
              url:
                lang === "ru"
                  ? "https://qjl.kz/ru/news/digitalisation-in-youth-football"
                  : "https://qjl.kz/en/news/digitalisation-in-youth-football",
            },
            {
              title:
                lang === "ru"
                  ? t("whitelabel.media.article2")
                  : "Soccer Is Using New Devices, Sensors to Evaluate Players' Technical Skills and Tactical Style",
              url:
                lang === "ru"
                  ? "https://dzen.ru/a/ZcFY7XY11GsTjwDt"
                  : "https://www.sportsbusinessjournal.com/Daily/Issues/2022/08/17/Technology/digital-scouting-data-metrics/",
            },
            {
              title:
                lang === "ru"
                  ? t("whitelabel.media.article3")
                  : "Comparison of JuniStat technology and laser systems for running tests",
              url:
                lang === "ru"
                  ? "https://ngs.ru/text/gorod/2025/06/18/75597434/"
                  : "https://medium.com/@junistat/comparison-of-junistat-technology-and-laser-systems-for-running-tests-baf08bc0c74d",
            },
          ].map((media) => {
            return (
              <div class="relative flex flex-col gap-4 bg-white hover:bg-white/50 p-4 rounded-2xl transition-colors">
                <a
                  href={media.url}
                  target="_blank"
                  class="before:z-10 before:absolute before:inset-0 before:size-full"
                >
                  <span class="font-medium md:text-2xl">{media.title}</span>
                </a>

                <span class="mt-auto text-sm">
                  {new URL(media.url).hostname.replace("www.", "")}
                </span>
              </div>
            );
          })
        }
      </div>
    </div>
  </section>

  <!-- CTA Banner -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <div
        class="flex flex-col justify-center items-center gap-6 p-4 md:p-12 py-6 rounded-2xl bg-accent-primary"
      >
        <h2 class="font-bold text-2xl/tight lg:text-5xl text-center">
          {t("whitelabel.cta.title")}
        </h2>

        <div
          class="flex md:flex-row flex-col justify-center max-md:items-stretch gap-4 w-full"
        >
          <button
            class="flex justify-center items-center gap-4 bg-white hover:bg-white/80 p-4 rounded-2xl w-fit max-md:w-full font-medium text-lg/tight lg:text-2xl transition-colors whitelabel-request-button"
          >
            <span>{t("whitelabel.cta.contact_manager")}</span>
            <SolarCalendarAddBold class="size-6 lg:size-12 shrink-0" />
          </button>
          <a
            href={lang === "ru"
              ? "https://junistat.com/documents/junicoach_step_by_step_ru.pdf"
              : "https://junistat.com/documents/junicoach_step_by_step_en.pdf"}
            target="_blank"
            class="flex justify-center items-center gap-4 bg-white/30 hover:bg-white/90 shadow-[inset_0_0_10px_#fff] p-4 border-2 border-white rounded-2xl w-fit max-md:w-full font-medium text-lg/tight lg:text-2xl transition-colors"
          >
            <span>{t("whitelabel.cta.instructions")}</span>
          </a>
        </div>
      </div>
    </div>
  </section>

  <footer class="mt-16 mb-10">
    <div class="text-center container">
      <p>{t("whitelabel.footer")}</p>
    </div>
  </footer>

  <!-- Request Modal -->
  <div
    id="request-modal"
    class="hidden z-50 fixed inset-0 bg-black/50 backdrop-blur-sm p-4 overflow-auto"
    role="dialog"
    aria-modal="true"
    aria-labelledby="request-modal-title"
  >
    <div class="flex justify-center m-auto h-fit">
      <div
        class="relative bg-white shadow-2xl rounded-2xl w-full max-w-md transition-all transform"
      >
        <!-- Modal Header -->
        <div
          class="flex justify-between items-center p-6 border-gray-100 border-b"
        >
          <h2 id="request-modal-title" class="font-bold text-gray-900 text-xl">
            {t("whitelabel.modal.title")}
          </h2>
          <button
            id="close-modal"
            class="hover:bg-gray-100 p-2 rounded-lg text-gray-400 hover:text-gray-600 transition-colors"
            aria-label={t("whitelabel.modal.close")}
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <!-- Modal Content -->
        <div class="p-6">
          <form id="request-form" class="space-y-4">
            <!-- Name Field -->
            <div>
              <label
                for="name"
                class="block mb-2 font-medium text-gray-700 text-sm"
              >
                {t("whitelabel.modal.name_label")}
              </label>
              <input
                type="text"
                id="name"
                name="name"
                required
                class="px-4 py-3 border border-gray-300 focus:border-blue-500 rounded-lg focus:ring-2 focus:ring-blue-500 w-full transition-colors"
                placeholder={t("whitelabel.modal.name_placeholder")}
              />
            </div>
            <!-- Email Field -->
            <div>
              <label
                for="email"
                class="block mb-2 font-medium text-gray-700 text-sm"
              >
                {t("whitelabel.modal.email_label")}
              </label>
              <input
                type="email"
                id="email"
                name="email"
                required
                class="px-4 py-3 border border-gray-300 focus:border-blue-500 rounded-lg focus:ring-2 focus:ring-blue-500 w-full transition-colors"
                placeholder={t("whitelabel.modal.email_placeholder")}
              />
            </div>
            <!-- Phone Field -->
            <div>
              <label
                for="phone"
                class="block mb-2 font-medium text-gray-700 text-sm"
              >
                {t("whitelabel.modal.phone_label")}
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                required
                class="px-4 py-3 border border-gray-300 focus:border-blue-500 rounded-lg focus:ring-2 focus:ring-blue-500 w-full transition-colors"
                placeholder={t("whitelabel.modal.phone_placeholder")}
              />
            </div>
            <!-- Academy Name Field -->
            <div>
              <label
                for="academy"
                class="block mb-2 font-medium text-gray-700 text-sm"
              >
                {t("whitelabel.modal.academy_label")}
              </label>
              <input
                type="text"
                id="academy"
                name="academy"
                required
                class="px-4 py-3 border border-gray-300 focus:border-blue-500 rounded-lg focus:ring-2 focus:ring-blue-500 w-full transition-colors"
                placeholder={t("whitelabel.modal.academy_placeholder")}
              />
            </div>
            <!-- Submit Button -->
            <button
              type="submit"
              class="flex justify-center items-center gap-2 hover:bg-primary-500 px-4 py-3 rounded-lg w-full font-medium text-text-primary transition-colors bg-accent-primary"
            >
              <span>{t("whitelabel.modal.submit")}</span>
            </button>
            <!-- Legal Text -->
            <p class="text-gray-500 text-xs text-center">
              {t("whitelabel.modal.legal")}
              <a
                href="/privacy"
                target="_blank"
                class="text-blue-600 hover:text-blue-800 underline"
              >
                {t("whitelabel.modal.privacy_link")}
              </a>
            </p>
          </form>
          <!-- Success Message -->
          <div id="success-message" class="hidden py-8 text-center">
            <div
              class="flex justify-center items-center bg-green-100 mx-auto mb-4 rounded-full w-16 h-16"
            >
              <svg
                class="w-8 h-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h3 class="mb-2 font-semibold text-gray-900 text-lg">
              {t("whitelabel.modal.success_title")}
            </h3>
            <p class="text-gray-600">{t("whitelabel.modal.success_message")}</p>
          </div>
          <!-- Error Message -->
          <div id="error-message" class="hidden py-8 text-center">
            <div
              class="flex justify-center items-center bg-red-100 mx-auto mb-4 rounded-full w-16 h-16"
            >
              <svg
                class="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
            <h3 class="mb-2 font-semibold text-gray-900 text-lg">
              {t("whitelabel.modal.error_title")}
            </h3>
            <p class="text-gray-600">
              {t("whitelabel.modal.error_message")}
            </p>
            <button
              id="retry-button"
              class="mt-4 text-blue-600 hover:text-blue-800 underline"
            >
              {t("whitelabel.modal.retry")}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener("DOMContentLoaded", function () {
      // Modal elements
      const modal = document.getElementById("request-modal") as HTMLElement;
      const openButtons = document.querySelectorAll(
        ".whitelabel-request-button"
      ) as NodeListOf<HTMLElement>;
      const closeButton = document.getElementById("close-modal") as HTMLElement;
      const form = document.getElementById("request-form") as HTMLFormElement;
      const successMessage = document.getElementById(
        "success-message"
      ) as HTMLElement;
      const errorMessage = document.getElementById(
        "error-message"
      ) as HTMLElement;
      const retryButton = document.getElementById(
        "retry-button"
      ) as HTMLElement;

      if (
        !modal ||
        !form ||
        !successMessage ||
        !errorMessage ||
        !closeButton ||
        !retryButton
      ) {
        return;
      }

      // Open modal
      openButtons.forEach((button) => {
        button.addEventListener("click", function () {
          modal.classList.remove("hidden");
          modal.classList.add("flex");
          document.body.style.overflow = "hidden";

          // Focus first input for accessibility
          const firstInput = form.querySelector("input");
          if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
          }
        });
      });

      // Close modal function
      function closeModal() {
        modal.classList.add("hidden");
        modal.classList.remove("flex");
        document.body.style.overflow = "";

        // Reset form and messages
        form.style.display = "block";
        successMessage.classList.add("hidden");
        errorMessage.classList.add("hidden");
      }

      // Close modal events
      closeButton.addEventListener("click", closeModal);

      // Close on escape key
      document.addEventListener("keydown", function (e) {
        if (e.key === "Escape" && !modal.classList.contains("hidden")) {
          closeModal();
        }
      });

      // Close on backdrop click
      modal.addEventListener("click", function (e) {
        if (e.target === modal) {
          closeModal();
        }
      });

      // Retry button
      retryButton.addEventListener("click", function () {
        form.style.display = "block";
        errorMessage.classList.add("hidden");
      });

      // Form submission
      form.addEventListener("submit", async function (e) {
        e.preventDefault();

        const formData = new FormData(form);
        const submitButton = form.querySelector(
          'button[type="submit"]'
        ) as HTMLButtonElement;
        const originalText = submitButton.innerHTML;

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = `
          <svg class="mr-2 w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Отправка...
        `;

        try {
          const response = await fetch(
            "https://n8n.kobro.ru/webhook/whitelabel",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                name: formData.get("name"),
                email: formData.get("email"),
                phone: formData.get("phone"),
                academyName: formData.get("academy"),
                lang: document.documentElement.getAttribute("lang") || "ru",
                source: "whitelabel",
              }),
            }
          );

          if (response.ok) {
            // Show success message
            form.style.display = "none";
            successMessage.classList.remove("hidden");

            // Auto close after 3 seconds
            setTimeout(() => {
              closeModal();
            }, 3000);
          } else {
            throw new Error("Network response was not ok");
          }
        } catch (error) {
          console.error("Error:", error);
          // Show error message
          form.style.display = "none";
          errorMessage.classList.remove("hidden");
        } finally {
          // Reset button state
          submitButton.disabled = false;
          submitButton.innerHTML = originalText;
        }
      });
    });
  </script>
</Layout>
