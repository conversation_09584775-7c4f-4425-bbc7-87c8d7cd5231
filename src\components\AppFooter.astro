---
//#region [i18n]
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "@lang/utils";

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]

//#region [Built-in components]
import { Image } from "astro:assets";
//#endregion [Built-in components]

//#region [Images]
import jsLogo from "@assets/images/js-jc-logo.svg";
//#endregion [Images]
---

<footer class="footer-wrap">
  <div id="footer" class="section-space">
    <div class="container">
      <div class="footer__grid">
        <!-- Logo -->
        <div class="footer__logo">
          <Image
            class="footer__logo-img"
            src={jsLogo}
            alt="JuniStat x JuniCoach"
          />
          <p class="footer__logo-copyright p3">
            JuniStat Corp © Delaware, USA
          </p>
        </div>
        <!-- Social links -->
        <div class="footer__social-links">
          <a
            class="footer__social-link"
            aria-label="Telegram"
            href="https://t.me/junistat_official"
            target="_blank"
            ><svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="32" height="32" rx="16" fill="black"></rect>
              <path
                d="M22.4 10.0819L19.9957 22.6339C19.9957 22.6339 19.6593 23.5042 18.7351 23.0868L13.1877 18.6822L13.1619 18.6692C13.9113 17.9724 19.7219 12.5622 19.9759 12.317C20.369 11.9372 20.1249 11.7111 19.6685 11.998L11.0854 17.6425L7.77411 16.4887C7.77411 16.4887 7.253 16.2968 7.20287 15.8794C7.15208 15.4613 7.79126 15.2352 7.79126 15.2352L21.2905 9.75126C21.2905 9.75126 22.4 9.24644 22.4 10.0819V10.0819Z"
                fill="white"></path>
            </svg>
          </a>
          <a
            class="footer__social-link"
            aria-label="Linkedin"
            href="https://www.linkedin.com/company/junistat"
            target="_blank"
            ><svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="32" height="32" rx="16" fill="black"></rect>
              <path
                d="M22.4 16.5956V21.0082H19.817V16.8647C19.817 15.8423 19.4404 15.1427 18.5256 15.1427C17.826 15.1427 17.3955 15.627 17.2341 16.0575C17.1803 16.2189 17.1265 16.4342 17.1265 16.7032V21.0082H14.5435C14.5435 21.0082 14.5973 14.0127 14.5435 13.3131H17.1265V14.3893C17.4493 13.8512 18.0951 13.0979 19.4404 13.0979C21.1085 13.0979 22.4 14.2279 22.4 16.5956ZM11.8529 9.6001C10.9919 9.6001 10.4 10.192 10.4 10.9454C10.4 11.6988 10.9381 12.2907 11.7991 12.2907C12.7139 12.2907 13.252 11.6988 13.252 10.9454C13.3058 10.1382 12.7677 9.6001 11.8529 9.6001ZM10.5614 21.0082H13.1444V13.3131H10.5614V21.0082Z"
                fill="white"></path>
            </svg>
          </a>
          <a
            class="footer__social-link"
            aria-label="Instagram"
            href="https://www.instagram.com/junistat.rating/"
            target="_blank"
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              fill="none"
              viewBox="0 0 32 32"
              ><path
                fill="#000"
                d="M32 16c0-8.837-7.163-16-16-16S0 7.163 0 16s7.163 16 16 16 16-7.163 16-16Z"
              ></path><path
                fill="#fff"
                d="M16 9.446h3.277c.771 0 1.157.193 1.446.289.385.193.675.29.964.578.289.29.482.579.578.964.096.29.193.675.29 1.446v6.554c0 .771-.194 1.157-.29 1.446-.193.385-.29.675-.578.964-.29.289-.579.482-.964.578-.29.096-.675.193-1.446.29h-6.554c-.771 0-1.157-.194-1.446-.29-.385-.193-.675-.29-.964-.578-.289-.29-.482-.579-.578-.964-.096-.29-.193-.675-.29-1.446v-6.554c0-.771.194-1.157.29-1.446.193-.385.29-.675.578-.964.29-.289.579-.482.964-.578.29-.096.675-.193 1.446-.29H16ZM16 8h-3.277c-.868 0-1.446.193-1.928.386a4.209 4.209 0 0 0-1.446.963c-.482.482-.674.868-.963 1.446-.193.482-.29 1.06-.386 1.928v6.554c0 .868.193 1.446.386 1.928.192.482.481.964.963 1.446s.868.674 1.446.963c.482.193 1.06.29 1.928.386h6.554c.868 0 1.446-.193 1.928-.386a4.21 4.21 0 0 0 1.446-.963c.482-.482.674-.868.963-1.446.193-.482.29-1.06.386-1.928v-6.554c0-.868-.193-1.446-.386-1.928a4.21 4.21 0 0 0-.963-1.446c-.482-.482-.868-.674-1.446-.963-.482-.193-1.06-.29-1.928-.386H16Z"
              ></path><path
                fill="#fff"
                d="M16 11.855A4.114 4.114 0 0 0 11.855 16 4.114 4.114 0 0 0 16 20.145 4.114 4.114 0 0 0 20.145 16 4.114 4.114 0 0 0 16 11.855Zm0 6.844A2.694 2.694 0 0 1 13.301 16a2.694 2.694 0 0 1 2.7-2.699 2.694 2.694 0 0 1 2.698 2.7c0 1.445-1.253 2.698-2.699 2.698Zm4.241-5.976a.964.964 0 1 0 0-1.928.964.964 0 0 0 0 1.928Z"
              ></path></svg
            >
          </a>
          <a
            class="footer__social-link"
            aria-label="Facebook"
            href="https://www.facebook.com/junistat.rating/"
            target="_blank"
            ><svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="32" height="32" rx="16" fill="black"></rect>
              <path
                d="M17.3257 16.2918H20.3034L20.7709 13.2483H17.3251V11.585C17.3251 10.3207 17.7357 9.1996 18.9111 9.1996H20.8V6.5437C20.4681 6.49861 19.7662 6.3999 18.44 6.3999C15.6706 6.3999 14.0469 7.87134 14.0469 11.2237V13.2483H11.2V16.2918H14.0469V24.6567C14.6107 24.742 15.1818 24.7999 15.7681 24.7999C16.298 24.7999 16.8151 24.7512 17.3257 24.6817V16.2918Z"
                fill="white"></path>
            </svg>
          </a>
        </div>
        <!-- Navigation -->
        <div class="footer__navigation p2">
          <a href={translatePath("/")}>{t("navigation.rating")}</a>
          <a href={translatePath("/parents")}>{t("navigation.parents")}</a>
          <a href={translatePath("/clubs")}>{t("navigation.clubs")}</a>
          <a href={translatePath("/sponsors")}>{t("navigation.sponsors")}</a>
          <a href={translatePath("/team")}>{t("navigation.team")}</a>
          <a href={translatePath("/faq")}>{t("navigation.faq")}</a>
          <a href={translatePath("/about")}>{t("navigation.about")}</a>
          <a
            href="https://drive.google.com/drive/folders/1SkYx9cxBiTqHiO37G-6QYQVDYLSY3fcM?usp=sharing"
            target="_blank">{t("navigation.press_kit")}</a
          >
          <a href={translatePath("/rating-system")}
            >{t("navigation.rating_system")}</a
          >
        </div>
        <div class="footer__legal p2">
          <a href="mailto:<EMAIL>" target="_blank"
            ><EMAIL></a
          >
          <a href="/privacy" rel="nofollow" target="_blank"
            >{t("navigation.privacy")}</a
          >
        </div>
      </div>
    </div>
  </div>
</footer>

<style lang="scss">
  @use "@styles/_variables.scss" as *;
  @use "@styles/_screens.scss" as *;

  #footer {
    background-color: $white;
    padding: $space-48 0;
  }

  .footer-wrap {
    margin-top: auto;
  }

  .footer__grid {
    display: grid;
    grid-template-areas:
      "a c c d"
      "b c c d";
    align-items: flex-start;
    column-gap: $space-80;

    @media screen and (max-width: $tablet) {
      grid-template-areas:
        "a b"
        "c c";
      row-gap: $space-24;
    }

    @media screen and (max-width: $mobile) {
      display: flex;
      flex-direction: column;
      row-gap: $space-24;
    }
  }

  .footer__logo-img {
    display: block;
    max-width: 18rem;
    margin-bottom: $space-8;
  }

  .footer__social-links {
    grid-area: b;
    display: flex;

    & > * + * {
      margin-left: $space-16;
    }

    @media screen and (max-width: $tablet) {
      display: flex;
      justify-content: flex-end;
    }
  }

  .footer__social-link,
  a {
    @include transition-opacity;

    &:hover {
      opacity: 50%;
    }
  }

  .footer__navigation {
    display: grid;
    grid-template-columns: repeat(2, auto);
    align-items: flex-start;
    grid-area: c;
    gap: $space-8;

    & a {
      line-height: 150%;
    }

    @media screen and (max-width: $mobile) {
      width: 100%;
      justify-content: space-between;
      grid-template-columns: 1fr;
    }
  }

  .footer__legal {
    display: flex;
    flex-direction: column;
    justify-self: end;

    & a {
      line-height: 150%;
    }

    @media screen and (max-width: $tablet) {
      justify-self: start;
    }
  }
</style>
