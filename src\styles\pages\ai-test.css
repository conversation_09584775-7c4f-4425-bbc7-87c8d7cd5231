/*#region [Font]*/
h1,
.h1 {
  font-weight: 800;
  font-size: 53px;
}

@media screen and (max-width: 688px) {
  h1,
  .h1 {
    font-size: 8vw;
  }
}

h2,
.h2 {
  font-weight: 800;
  font-size: 48px;
}

@media screen and (max-width: 688px) {
  h2,
  .h2 {
    font-size: 26px;
  }
}

h3,
.h3 {
  font-weight: 600;
  font-size: 32px;
}

@media screen and (max-width: 688px) {
  h3,
  .h3 {
    font-size: 22px;
  }
}

p,
.p {
  font-size: 16px;
}
/*#endregion [Font]*/

.overflow-visible {
  overflow: visible;
}

/*#region [Hero]*/
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
/*#endregion [Hero]*/

/*#region [Hero]*/
.section.is--hero {
  position: relative;
}

.hero__content {
  position: relative;
  z-index: 5;
}

.hero {
  margin-top: 55px;
  margin-bottom: 88px;
}

.hero__headline {
  margin-top: 64px;
}

.hero__headline > sup {
  font-size: 0.6em;
}

.hero__subtitle {
  max-width: 740px;
  margin-top: 32px;
}

.hero__gradient {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 1;
  background: linear-gradient(180deg, #eaedf9 0%, rgba(244, 245, 249, 0) 100%);
}

.container.is--hero-bg {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.hero__bg-image {
  position: absolute;
  top: -10%;
  right: -10%;
  width: 560px;
  z-index: 0;
  mix-blend-mode: color-burn;

  @media screen and (max-width: 688px) {
    top: 0%;
    right: -50%;
  }
}
/*#endregion [Hero]*/

/*#region [Tests]*/
.container.is--tests {
  position: relative;
}

.tests {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(6, 1fr);
  grid-auto-rows: auto;
}

@media screen and (max-width: 688px) {
  .tests {
    display: flex;
    gap: 0;
  }
}

.tests__show-more-wrap {
  display: none;
}

@media screen and (min-width: 767px) {
  .tests.is--preview > *:nth-child(n + 8) {
    display: none;
  }

  .tests.is--preview > *:nth-child(7) {
    height: 200px;
    overflow: hidden;
  }

  .tests__show-more-wrap {
    position: absolute;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    background: linear-gradient(
      180deg,
      rgba(244, 245, 249, 0.4) 0%,
      rgb(244 245 249) 80%
    );
    z-index: 100;
  }

  #show-more-tests {
    font-size: 24px;
  }
}

/*#region [Pagination]*/
.tests__pagination-wrap {
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  min-height: 48px;
  margin-top: 32px;
  display: none;
  position: sticky;
  z-index: 1000;
}

@media screen and (max-width: 689px) {
  .tests__pagination-wrap {
    display: flex;
  }
}

.tests__pagination {
  width: 100%;
  display: flex;
  gap: 24px;
  justify-content: center;
  align-items: center;
  background: rgba(217, 217, 217, 0.7);
  border: 1px solid #f1f1f1;
  border-radius: 8px;
  padding: 0.25rem;
}

.tests__pagination-button {
  background: rgba(255, 255, 255, 0.8);
  width: 100%;
}

@media screen and (min-width: 992px) {
  .tests__pagination-button:hover {
    background: rgba(255, 255, 255, 1);
  }
}

.tests__pagination-counter {
  white-space: nowrap;
  text-align: center;
}
/*#endregion [Pagination]*/

/*#endregion [Tests]*/

/*#region [Features]*/
.features__headline {
  max-width: 800px;
}

.features__wrap {
  display: flex;
  column-gap: 162px;
  row-gap: 32px;
  justify-content: space-between;
  margin-top: 64px;
}

@media screen and (max-width: 688px) {
  .features__wrap {
    flex-wrap: wrap;
    row-gap: 16px;
    margin-top: 32px;
  }
}

.features__card {
  display: flex;
  align-items: center;
  max-width: 50%;
}

@media screen and (max-width: 688px) {
  .features__card {
    max-width: none;
  }
}

.features__card-icon {
  --icon-size: 128px;

  margin-right: 24px;
  width: var(--icon-size);
  height: var(--icon-size);
  flex-shrink: 0;
}

@media screen and (max-width: 688px) {
  .features__card-icon {
    --icon-size: 64px;
  }
}
/*#endregion [Features]*/

/*#region [Price]*/
.price__header {
  width: 100%;
}

.price__headline {
  max-width: 600px;
}

@media screen and (max-width: 688px) {
  .price__headline {
    max-width: none;
  }
}

.price__card {
  gap: 32px;
  width: 100%;
  padding: 32px;

  border-radius: 16px;
  background: var(--white);
}

@media screen and (max-width: 688px) {
  .price__card {
    padding: 24px;
  }
}

.price__content {
  margin-top: 48px;
  position: relative;
}

.price__grid {
  display: grid;
  grid-template-columns: 0.5fr 0.5fr 0.7fr;
  gap: 16px;
  grid-template-areas:
    "card1 card1 form"
    "card3 card3 form"
    "d d form";
}

@media screen and (max-width: 800px) {
  .price__grid {
    grid-template-columns: 1fr;
    grid-template-areas:
      "card1"
      "card2"
      "card3"
      "form"
      "d";
  }
}

.price__price-cell {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.price__cell-description {
  margin-top: 16px;
}

.price__price-cell > * + * {
  margin-top: 16px;
}

.price__price-amount {
  font-size: 40px;
  font-weight: 600;
  line-height: 90%;
  font-stretch: 110%;
}

.price__cards {
  margin-top: 24px;
  display: flex;
  gap: 24px;
  width: 100%;
}

@media screen and (max-width: 810px) {
  .price__cards {
    flex-direction: column;
  }
}

.text-link {
  color: #0d99ff;
  text-decoration: underline;
}

@media (hover: hover), (-moz-touch-enabled: 0), (pointer: fine) {
  .text-link:hover {
    color: #0e5d96;
  }
}

.price__distr-country-name {
  font-weight: 600;
}

.price__distr-list {
  display: flex;
  flex-direction: column;
}

.price__distr-list > .text-link::after {
  content: "↗";
  position: absolute;
}

/*#region [Form]*/

.price__form {
  margin-top: 40px;
}

.price__form > label {
  display: block;
}

.price__form > label + label {
  margin-top: 24px;
}

.price__form-label {
  display: block;
}

.price__form-input {
  padding: 8px 16px;
  width: 100%;
  background-color: #f4f5f9;
  border: none;
  border-radius: 6px;
}

.price__form-agrement {
  margin-top: 24px;
  margin-bottom: 24px;
}

.form__success {
  display: none;
  padding: 16px;
  background-color: rgb(15, 143, 49);
  border-radius: 8px;
  color: white;
  margin-top: 24px;
}

.form__error {
  display: none;
  padding: 16px;
  background-color: rgb(143, 15, 49);
  border-radius: 8px;
  color: white;
  margin-top: 24px;
}

/*#endregion [Form]*/

.price__card-user {
  width: 100%;
  margin: 0 auto;
  max-width: 500px;
  padding: 24px;
  background-color: var(--white);
  display: flex;
  gap: 16px;
  flex-direction: column;
  align-items: center;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.price__card-user.is--free-tests::before {
  content: "15 free tests";
  display: flex;
  justify-content: center;
  width: 300px;
  background-color: var(--primary);
  position: absolute;
  top: 10px;
  right: -90px;
  transform: rotate(20deg);
}

.price__card-button {
  font-size: 24px;
  width: 100%;
  max-width: 80%;
  margin-top: auto;
}

/*#endregion [Price]*/

/*#region [Footer]*/
footer {
  margin-bottom: 56px;
}

.container.is--footer {
  text-align: center;
}
/*#endregion [Footer]*/
