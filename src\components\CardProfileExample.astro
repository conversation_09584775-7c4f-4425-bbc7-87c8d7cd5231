---
//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
//#endregion [i18n]

import Image from "astro/components/Image.astro";

import CardRuImg from "@assets/images/example-cards/v2/card-scheme-ru-v2-small.png";
import CardEsImg from "@assets/images/example-cards/card-scheme-es.png";
import CardCaImg from "@assets/images/example-cards/card-scheme-ca.png";
import CardUFSImg from "@assets/images/example-cards/card-scheme-ufs-ru.png";

interface Props {
  flag?: "ru" | "es" | "ca" | "ufs";
  class?: string;
  info?: boolean;
  lazy?: boolean;
}

const { flag, class: classList, info, lazy = true } = Astro.props;

const loading = lazy ? "lazy" : "eager";

const selectedCard = (() => {
  switch (flag) {
    case "ru":
      return CardRuImg;
    case "es":
      return CardEsImg;
    case "ca":
      return CardCaImg;
    case "ufs":
      return CardUFSImg;
    default:
      return CardEsImg;
  }
})();
---

<!-- Exmaple -->
<div class="example">
  <Image
    loading={loading}
    src={selectedCard}
    alt="Card example"
    class:list={["example__card-img", classList]}
  />
  {
    info && !Astro.slots.info && (
      <div class="example__info">
        <h2 class="example__info-title">{t("rating.example.title")}</h2>

        <ul class="example__info-list">
          <li class="example__info-list-item">
            <h3>{t("rating.example.point1")}</h3>
          </li>
          <li class="example__info-list-item">
            <h3>{t("rating.example.point2")}</h3>
          </li>
          <li class="example__info-list-item">
            <h3>{t("rating.example.point3")}</h3>
          </li>
          <li class="example__info-list-item">
            <h3>{t("rating.example.point4")}</h3>
          </li>
        </ul>
      </div>
    )
  }
  <slot name="info" />
</div>

<style lang="scss">
  @use "@styles/_variables.scss" as v;
  @use "@styles/_screens.scss" as screen;

  /*#region [Example]*/

  .example {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    gap: v.$space-64;

    @media screen and (max-width: screen.$tablet) {
      flex-direction: column;
    }
  }

  .example__card-img,
  .example__info {
    max-width: 480px;
  }

  .example__info-list {
    margin-top: v.$space-32;

    * + * {
      margin-top: v.$space-24;

      @media screen and (max-width: screen.$tablet) {
        margin-top: v.$space-16;
      }
    }
  }
  /*#endregion [Example]*/
</style>
