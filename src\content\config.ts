// Import utilities from `astro:content`
import { z, defineCollection } from "astro:content";
// Define a `type` and `schema` for each collection
const articlesCollection = defineCollection({
  type: "content",
  schema: ({ image }) =>
    z.object({
      title: z.string(),
      date: z.date(),
      cover: image(),
      author: z.string().optional(),
    }),
});
// Export a single `collections` object to register your collection(s)
export const collections = {
  hc: articlesCollection,
  "hc-ru": articlesCollection,
  blog: articlesCollection,
  "blog-ru": articlesCollection,
};

// // Import utilities from `astro:content`
// import { z, defineCollection } from "astro:content";
// // Define a `type` and `schema` for each collection
// const postsCollection = defineCollection({
//   type: "content",
//   schema: ({ image }) =>
//     z.object({
//       title: z.string(),
//       created_at: z.date(),
//       image: z
//         .object({
//           url: image().optional(),
//           alt: z.string().optional(),
//         })
//         .optional(),
//       tags: z.array(z.string()),
//     }),
// });
// // Export a single `collections` object to register your collection(s)
// export const collections = {
//   posts: postsCollection,
// };
