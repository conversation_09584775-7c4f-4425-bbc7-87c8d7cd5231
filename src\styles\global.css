@import "tailwindcss";

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Regular.woff2") format("woff2");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Medium.woff2") format("woff2");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-SemiBold.woff2") format("woff2");
  font-weight: 600;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Bold.woff2") format("woff2");
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}

@theme {
  --default-font-family: "Onest", "sans-serif";

  /* Neutral Scale */
  --color-neutral-50: #f4f5f9;
  --color-neutral-100: #e5e7eb;
  --color-neutral-200: #d1d5db;
  --color-neutral-300: #9ca3af;
  --color-neutral-400: #6b7280;
  --color-neutral-500: #4b5563;
  --color-neutral-600: #374151;
  --color-neutral-700: #1f2937;
  --color-neutral-800: #111827;
  --color-neutral-900: #292f32;

  /* Primary (Yellow) Scale */
  --color-primary-50: #fffbeb;
  --color-primary-100: #fef3c7;
  --color-primary-200: #fde68a;
  --color-primary-300: #fcd34d;
  --color-primary-400: #ffe83f;
  --color-primary-500: #fcdb05;
  --color-primary-600: #d97706;
  --color-primary-700: #b45309;
  --color-primary-800: #92400e;
  --color-primary-900: #78350f;

  /* Success (Green) Scale */
  --color-success-50: #ecfdf5;
  --color-success-100: #d1fae5;
  --color-success-200: #a7f3d0;
  --color-success-300: #6ee7b7;
  --color-success-400: #2ed887;
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
  --color-success-800: #065f46;
  --color-success-900: #064e3b;

  /* Text Colors */
  --color-text-primary: var(--color-neutral-900);
  --color-text-secondary: var(--color-neutral-600);
  --color-text-muted: var(--color-neutral-500);
  --color-text-inverse: #ffffff;

  /* Background Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: var(--color-neutral-50);
  --color-bg-accent: var(--color-primary-400);
  --color-bg-success: var(--color-success-400);

  /* Border Colors */
  --color-border-primary: var(--color-neutral-200);
  --color-border-accent: var(--color-primary-400);
  --color-border-success: var(--color-success-400);

  /* Interactive States */
  --color-accent-primary: var(--color-primary-400);
  --color-accent-success: var(--color-success-400);
  --color-hover-accent: var(--color-primary-500);
  --color-hover-success: var(--color-success-500);

  --default-transition-duration: 300ms;
}

@layer base {
  body {
    @apply bg-bg-secondary text-text-primary;
  }
  button {
    cursor: pointer;
  }
  abbr {
    text-decoration: none;
  }

  img {
    width: 100%;
    height: auto;
    object-fit: contain;
  }

  section {
    overflow: hidden;
  }
}

@utility container {
  @apply mx-auto px-4;

  @media (width >= 0) {
    @apply max-w-screen-xl;
  }
}
