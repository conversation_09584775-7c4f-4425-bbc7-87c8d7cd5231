@use "sass:color";
@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Download buttons]*/
.download-buttons {
  display: flex;
  gap: $space-16;
  margin-top: $space-32;

  & img {
    max-height: $space-64;
  }

  @media screen and (max-width: $mobile) {
    margin-top: $space-48;
  }
}

.download-button {
  @include transition-opacity;
  &:hover {
    opacity: 60%;
  }
}

.download-button-img {
  height: 100%;
}
/*#endregion [Download buttons]*/

/*#region [Hero]*/
.hero {
  margin-top: $space-48;
}

.hero__top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  column-gap: $space-64;

  @media screen and (max-width: 1140px) {
    flex-direction: column;
    row-gap: $space-32;
  }
}

.hero__text-side {
  max-width: 680px;
  flex: 0 1 60%;
}

.hero__headline {
  font-size: 3rem;
  margin-bottom: $space-16;

  @media screen and (max-width: $tablet) {
    font-size: 2.1rem;
  }
}

.hero__comparison-wrap {
  padding: $space-24 $space-48;
  color: $white;
  background-color: $black;
  display: flex;
  flex-direction: column;
  row-gap: $space-16;
  border-radius: $space-8;

  @media screen and (min-width: $tablet) {
    transform: skewX(-10deg);

    & > * {
      transform: skewX(10deg);
    }
  }

  @media screen and (max-width: $tablet) {
    row-gap: $space-8;
    padding: $space-24 $space-24;
    background-color: transparent;
    padding: 0;
  }
}

.hero__comparison-point {
  display: flex;
  align-items: center;

  &:before {
    content: "";
    display: block;
    width: 8px;
    height: 24px;
    margin-right: $space-16;
    flex-shrink: 0;
    background-color: $primary;
    transform: skewX(-10deg);
    border-radius: 2px;
  }

  @media screen and (max-width: 1140px) {
    align-items: flex-start;
    padding: $space-16 $space-16;
    color: $white;
    background-color: $black;
    border-radius: $space-8;
  }
}

.hero__comparison-point-text {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  column-gap: $space-24;

  & span:first-child {
    width: max-content;
    flex-shrink: 0;
  }

  @media screen and (max-width: $tablet) {
    flex-direction: column;
    align-items: start;
    row-gap: 0.25rem;
  }
}

.hero__comparison-point-text-vs {
  @media screen and (max-width: $tablet) {
    opacity: 0.7;
  }
}

.hero__cta-button-wrap {
  margin-top: $space-8;
  background-color: $white;
  border-radius: $space-8;
  background: radial-gradient(
      51.55% 35.75% at 50.05% 49.74%,
      rgba(255, 215, 63, 0.3) 20.14%,
      rgba(255, 232, 63, 0.3) 64.93%,
      rgba(255, 255, 255, 0.3) 100%
    ),
    #ffffff;

  @media screen and (min-width: $tablet) {
    transform: skewX(-10deg) translateX(-27px);

    .button--primary > * {
      transform: skewX(10deg);
    }
  }
}

.hero__cta-button-description {
  padding: $space-8 $space-8;
  text-align: center;

  @media screen and (min-width: $desktop) {
    transform: skewX(10deg);
  }
}

.button--mod2 * {
  font-weight: 600;
}

.hero__cta-button-icon {
  width: 2.5rem;
  height: 2.5rem;
}

.hero__request-presentation-button {
  color: white;
  width: 100%;
  background-color: #242424;
  margin-top: 8px;
}

.hero__request-presentation-button:hover {
  background-color: #4f4f4f;
}

.hero__video {
  margin-top: $space-48;
}

.hero__video-tag {
  background-size: cover;
}

.hero__video {
  border-radius: 1rem;
  overflow: hidden;
}

.video {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 10;
  overflow: hidden;
  border-radius: 16px;
  isolation: isolate;
}

.video__play-button {
  display: none;
  width: 80px;
  height: 80px;
  position: absolute;
  z-index: 15;
  pointer-events: none;
}

video {
  width: 100%;
}
/*#endregion [Hero]*/

/*#region [Presentation modal]*/
.presentation-modal {
  display: none;
  position: fixed;
  inset: 0;
  padding: 0 16px;
  z-index: 1000;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  transition: opacity 0.3s ease;
}

.presentation-modal__container {
  position: relative;
  background-color: white;
  border-radius: 8px;
  width: 500px;
  padding: 16px;
}

.presentation-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.presentation-modal__title {
  font-weight: 600;
}

.presentation-modal__close {
  cursor: pointer;
}

@media (any-hover: hover) {
  .presentation-modal__close:hover {
    color: #0e5d96;
  }
}

// Form
.presentation-modal__form {
  padding-top: 24px;
}

.presentation-modal__input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 16px;
}

.presentation-modal__button {
  width: 100%;
}

.presentation-modal__legal-text {
  display: inline-block;
  font-size: 14px;
  line-height: 120%;
  margin-top: 16px;
}

.text-link {
  color: #0d99ff;
  text-decoration: underline;
}

@media (any-hover: hover) {
  .text-link:hover {
    color: #0e5d96;
  }
}

.form__success {
  display: none;
  padding: 16px;
  background-color: rgb(15, 143, 49);
  border-radius: 8px;
  color: white;
  margin-top: 24px;
}

.form__error {
  display: none;
  padding: 16px;
  background-color: rgb(143, 15, 49);
  border-radius: 8px;
  color: white;
  margin-top: 24px;
}
/*#endregion [Presentation modal]*/

/*#region [How works]*/

.how__headline {
  margin-bottom: $space-48;
}

@media screen and (max-width: 688px) {
  .how__headline {
    margin-bottom: 24px;
  }
}

.how__points {
  display: flex;
  justify-content: space-between;
  column-gap: $space-80;

  @media screen and (max-width: $tablet) {
    flex-direction: column;
    gap: $space-48;
  }
}

.how__column {
  flex: 1 1 50%;

  .download-buttons {
    margin-top: $space-24;
  }
}

.how__point-icon {
  width: $space-64;
  height: $space-64;
}

.how__equip {
  margin-top: $space-80;
}

.how__equip-content {
  margin-top: $space-32;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto;
  gap: $space-32 $space-32;
  align-items: stretch;

  @media screen and (max-width: $tablet) {
    grid-template-columns: 1fr;
  }
}

.how__equip-col.is--items {
  display: grid;
  grid-template: auto auto / auto auto;
  gap: $space-16 $space-16;
  align-self: flex-start;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.how__equip-item {
  display: flex;
  align-items: center;
  gap: $space-16 $space-16;
  border-radius: $space-8;
  background-color: $white;
  padding: $space-16;
}

.how__equip-item-img {
  display: block;
  width: 128px;
  height: 128px;

  @media screen and (max-width: $tablet) {
    width: 72px;
    height: 72px;
  }
}

.how__equip-photo {
  display: block;
  overflow: hidden;
  border-radius: $space-8;
}

.how__equip-button {
  grid-column: 1 / 2 span;
}

/*#endregion [How works]*/

/*#region [Exercises]*/
.exercises {
  display: grid;
  grid-template-columns: 0.5fr 1fr;
  gap: $space-48;

  @media screen and (max-width: $tablet) {
    grid-template-columns: 1fr;
  }
}

.exercises__title {
  margin-bottom: $space-32;
}

.container.is--exercises {
  @media screen and (max-width: $tablet) {
    padding: 0;

    .exercises__header {
      padding: 0 $space-24;
    }
    .tests__images {
      padding: 0 $space-24;
    }
  }
}

.tests__images {
  width: 100%;
  column-count: 3;

  @media screen and (max-width: $mobile) {
    display: flex;
    align-items: flex-start;
    overflow: scroll;
    column-count: 1;
    gap: $space-16;
  }
}

.tests__img {
  display: block;
  margin-bottom: $space-16;

  @media screen and (max-width: $mobile) {
    width: 280px;
    height: auto;
    flex: 1 0 280px;
  }
}
/*#endregion [Exercises]*/

/*#region [Recommends]*/
.recommends {
  display: grid;
  grid-template-columns: 0.7fr 1fr;
  grid-template-rows: auto auto;
  column-gap: $space-24;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.recommends__title {
  @media screen and (max-width: $mobile) {
    margin-bottom: $space-24;
  }
}

.recommends__slider {
  position: relative;
  grid-column: 2;
  grid-row: 1 / 2 span;
}

.recommends__slider-container {
  @media screen and (max-width: $mobile) {
    overflow: visible;
  }
}

.recommends__slider-shadow {
  position: absolute;
  width: 2.5rem;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(90deg, #f4f5f9 0%, rgba(244, 245, 249, 0) 100%);
  z-index: 1000;
  pointer-events: none;

  @media screen and (max-width: $mobile) {
    display: none;
  }
}

.recommends__slide {
  width: 38%;
  z-index: 1;
  padding-top: 1rem;
  border-radius: $space-8;

  @include transition-opacity;
  @include transition-transform;

  @media screen and (min-width: $tablet) {
    &:hover {
      transform: translateY(-1rem);
    }
  }

  @media screen and (max-width: $mobile) {
    width: 58%;
  }
}

.recommends__slider-controls {
  display: flex;
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    margin-top: $space-32;
  }
}

.recommends__slider-button {
  width: 5.5rem;
  height: 5.5rem;
  padding: $space-16;
  background-color: $white;
  border: solid 1px $gray;
  border-radius: $space-8;
  cursor: pointer;
  @include transition-colors;

  @media screen and (min-width: $tablet) {
    &:hover {
      background-color: $primary;
    }
  }

  @media screen and (max-width: $mobile) {
    width: 100%;
    height: 3rem;
    padding: $space-8;
  }
}

.recommends__slider-button.swiper-button-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.recommends-people {
  margin-top: $space-80;
}

.recommends-people__card {
  display: flex;
  flex-direction: column;
  gap: $space-16;

  &.is--ian {
    max-width: 720px;
    @media screen and (max-width: $mobile) {
      flex-direction: column;
    }

    .recommends-people__card-text:not(.is--open) {
      max-height: 3rem;
    }
  }
}

.recommends-people__card-text-wrap {
  display: flex;
  flex-direction: column;
  row-gap: $space-16;
}

.recommends-people__card-text:not(.is--open) {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recommends-people__card-text-expand {
  max-height: $space-24;
}

.recommends-people__card-author {
  display: flex;
  align-items: center;
  gap: $space-16;
  line-height: 100%;
  flex-shrink: 0;
}

.recommends-people__card-author-img {
  width: 4rem;
  height: 4rem;
  border-radius: 100%;
  overflow: hidden;
  flex-shrink: 0;
}
/*#endregion [Recommends]*/

/*#region [Features]*/
#features {
  overflow: visible;

  @media screen and (max-width: $mobile) {
    overflow: hidden;
  }
}

.features {
  display: grid;
  grid-template-columns: 1fr 1fr;

  @media screen and (max-width: $tablet) {
    grid-template-columns: 1fr;
  }
}

.features__card-profile-wrap {
  display: flex;
  column-gap: $space-48;
  position: relative;
}

.features__card-girl {
  position: absolute;
  z-index: 1;
  top: 0;
  right: -50%;
  max-width: 280px;

  @media screen and (max-width: $tablet) {
    right: 0%;
  }

  @media screen and (max-width: $mobile) {
    display: none;
  }
}

.features__card-profile {
  display: flex;
  grid-row: 1/ -1;
  align-items: flex-start;
}

.example__card-img {
  position: sticky;
  top: $space-80;
  z-index: 10;
}

.features__profile-description {
  margin-top: 80%;

  @media screen and (max-width: $tablet) {
    margin-top: 10%;
  }
}

.features__profile-title {
  margin-bottom: $space-48;
}

.features__profile-point + .features__profile-point {
  margin-top: $space-24;
}

.features__profile-point {
  display: flex;

  &:before {
    content: "";
    display: block;
    width: 12px;
    height: 32px;
    margin-right: $space-24;
    flex-shrink: 0;
    background-color: $primary;
    transform: skewX(-10deg);
    border-radius: 2px;
    border: 1px solid rgb(199, 199, 129);
  }
}

.features__blocks {
  display: flex;
  flex-direction: column;
  row-gap: $space-80;

  @media screen and (max-width: $mobile) {
    overflow: hidden;
    row-gap: $space-48;
  }
}

.features__skills {
  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

/*#region [Skills]*/
.features__skills-grid {
  margin-top: $space-24;
  column-count: 2;
  column-gap: $space-16;

  @media screen and (max-width: $tablet) {
    column-count: 2;
  }

  @media screen and (max-width: $mobile) {
    display: flex;
    overflow: auto;
    column-count: 1;
    gap: $space-16;
  }
}

.skills-card {
  @media screen and (max-width: $mobile) {
    flex-shrink: 0;
    width: 320px;
  }
}

.skills-card + .skills-card {
  margin-top: $space-16;

  @media screen and (max-width: $mobile) {
    margin-top: 0;
  }
}
/*#endregion [Skills]*/

/*#region [Video]*/
.features__videos-wrap {
  margin-top: $space-24;
  padding: $space-16;
  background-color: $white;
  border-radius: $space-8;
  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.06),
    0px 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  gap: $space-16;
}

.features__video-wrap {
  position: relative;
  border-radius: $space-8;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  @media screen and (max-width: $mobile) {
    &:nth-child(3) {
      display: none;
    }
  }
}

.features__video-img {
  width: 100%;
}

.features__video-play-icon {
  position: absolute;
  filter: drop-shadow(0px 1px 18px rgba(0, 0, 0, 0.59));
}
/*#endregion [Video]*/

/*#region [Data]*/
.features__data-wrap {
  margin-top: $space-24;
  padding: $space-16;
  background-color: $white;
  border-radius: $space-8;
  box-shadow:
    0px 1px 2px rgba(0, 0, 0, 0.06),
    0px 1px 4px rgba(0, 0, 0, 0.1);
  column-gap: $space-16;
  display: flex;
  flex-direction: column;
  row-gap: $space-16;
}

.features__params-list {
  display: flex;
  flex-direction: row;
  column-gap: $space-32;

  @media screen and (max-width: $tablet) {
    flex-wrap: wrap;
    column-gap: $space-24;
    row-gap: $space-16;
  }
}

.features__param {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  column-gap: $space-8;
}

.features__param-name {
  font-weight: 700;
  font-size: 18px;
  word-break: keep-all;
}

.features__param-value {
  font-size: 24px;
}

.features__data-lock {
  margin-top: $space-32;
  display: flex;
  column-gap: $space-32;
  align-items: center;
}

@media screen and (max-width: 688px) {
  .features__data-lock {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.features__data-lock-circle {
  border-radius: 100%;
  width: 4.5rem;
  height: 4.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $primary;
  flex-shrink: 0;
}

/*#endregion [Data]*/

/*#endregion [Features]*/

/*#region [Steps]*/
#steps {
  padding: $space-64 0px;
  color: $white;
}

.steps-grid {
  display: flex;
  justify-content: space-between;
  gap: 8rem;
  margin-top: $space-80;

  @media screen and (max-width: $tablet) {
    flex-direction: column;
    gap: $space-32;
  }
}

body.ru .steps__headline {
  max-width: 80%;

  @media screen and (max-width: $tablet) {
    max-width: none;
  }
}

.steps__step-card {
  flex: 1 1 0;
}

.steps__step-icon-wrap {
  width: 4.5rem;
  height: 4.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  margin-bottom: $space-16;
  background-color: $primary;
}

.steps__registration-button {
  margin-top: $space-48;
}

.clubs__registration-description {
  margin-top: $space-16;
  text-align: center;
}
/*#endregion [Steps]*/

/*#region [Recommends]*/
.recommends {
  display: grid;
  grid-template-columns: 0.7fr 1fr;
  grid-template-rows: auto auto;
  column-gap: $space-24;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.recommends__title {
  @media screen and (max-width: $mobile) {
    margin-bottom: $space-24;
  }
}

.recommends__slider {
  position: relative;
  grid-column: 2;
  grid-row: 1 / 2 span;
}

.recommends__slider-container {
  @media screen and (max-width: $mobile) {
    overflow: visible;
  }
}

.recommends__slider-shadow {
  position: absolute;
  width: 2.5rem;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(90deg, #f4f5f9 0%, rgba(244, 245, 249, 0) 100%);
  z-index: 1000;
  pointer-events: none;

  @media screen and (max-width: $mobile) {
    display: none;
  }
}

.recommends__slide {
  width: 38%;
  z-index: 1;
  padding-top: 1rem;
  border-radius: $space-8;

  @include transition-opacity;
  @include transition-transform;

  @media screen and (min-width: $tablet) {
    &:hover {
      transform: translateY(-1rem);
    }
  }

  @media screen and (max-width: $mobile) {
    width: 58%;
  }
}

.recommends__slider-controls {
  display: flex;
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    margin-top: $space-32;
  }
}

.recommends__slider-button {
  width: 5.5rem;
  height: 5.5rem;
  padding: $space-16;
  background-color: $white;
  border: solid 1px $gray;
  border-radius: $space-8;
  cursor: pointer;
  @include transition-colors;

  @media screen and (min-width: $tablet) {
    &:hover {
      background-color: $primary;
    }
  }

  @media screen and (max-width: $mobile) {
    width: 100%;
    height: 3rem;
    padding: $space-8;
  }
}

.recommends__slider-button.swiper-button-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.recommends-people {
  margin-top: $space-80;
}

.recommends-people__card {
  display: flex;
  flex-direction: column;
  gap: $space-16;
  line-height: 140%;

  &.is--ian {
    max-width: 720px;

    @media screen and (max-width: $mobile) {
      flex-direction: column;
    }

    & .recommends-people__card-text:not(.is--open) {
      max-height: 3rem;
    }
  }
}

.recommends-people__card-text-wrap {
  display: flex;
  flex-direction: column;
  row-gap: $space-16;
}

.recommends-people__card-text:not(.is--open) {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recommends-people__card-text-expand {
  max-height: $space-24;
}

.recommends-people__card-author {
  display: flex;
  align-items: center;
  gap: $space-16;
  line-height: 100%;
  flex-shrink: 0;
}

.recommends-people__card-author-img {
  width: 4rem;
  height: 4rem;
  border-radius: 100%;
  overflow: hidden;
  flex-shrink: 0;
}
/*#endregion [Recommends]*/

/*#region [Footer cards]*/
.footer-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.footer-cards__card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: $space-24;
  text-align: center;
  gap: $space-24;

  background-color: $white;
  border-radius: $space-8;
  border: 1px solid $gray;

  @include transition-colors;

  &:hover {
    background-color: color.change($white, $lightness: 97%);
  }

  &.is--material {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    @media screen and (max-width: $mobile) {
      flex-direction: column-reverse;
      align-items: flex-start;
    }
  }

  &.is--accent {
    background-color: $primary;
  }
}

.footer-cards__card-side {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: $space-8;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-text {
  width: 80%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-icon {
  width: $space-64;
  height: $space-64;
}

.download-buttons.is--footer {
  justify-content: center;
  align-items: center;
  margin-top: 0;
}
/*#endregion [Footer cards]*/
