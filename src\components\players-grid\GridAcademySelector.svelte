<script lang="ts">
  //#region [i18n]
  import {
    getLangFromUrl,
    useTranslations,
    useTranslatedPath,
  } from "@lang/utils";
  export let lang = "en";
  const t = useTranslations(lang);
  //#endregion [i18n]

  import { franchise } from "./academyStore";

  interface Academy {
    name: string;
    franchiseId: string;
    academyId: string;
  }

  export let academies: Array<Academy> = [];

  if (academies.length >= 1) {
    franchise.set({
      id: academies[0].franchiseId,
      academyId: academies[0].academyId,
    });
  }

  function selectAcademy(academy: Academy) {
    franchise.set({
      id: academy.franchiseId,
      academyId: academy.academyId,
    });
  }
</script>

{#if academies.length > 1}
  <div class="tab">
    {#each academies as academy}
      <button
        class="tab__item"
        tabindex="0"
        class:current={$franchise.id === academy.franchiseId &&
          $franchise.academyId === academy.academyId}
        on:click={() => selectAcademy(academy)}
        on:keypress={(e) => {
          e.key === "Enter" && selectAcademy(academy);
        }}
      >
        {academy.name}
      </button>
    {/each}
  </div>
{:else}
  <h2>{t("rating.titleAllPlayers")}</h2>
{/if}

<style>
  .tab {
    display: flex;
    flex-direction: row;
    overflow: auto;
    width: fit-content;
    max-width: 100%;
    background-color: var(--white);
    border-radius: 8px;
    padding: 6px;
    height: 56px;
  }

  .tab > * + * {
    margin-left: 12px;
  }

  .tab__item {
    color: #545454;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    flex-shrink: 0;

    transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .tab__item.current {
    background-color: var(--primary);
    color: var(--black);
  }

  @media screen and (min-width: 996px) {
    .tab__item:hover:not(.current) {
      background-color: #f3f3f3;
    }
  }
</style>
