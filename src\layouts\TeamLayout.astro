---
import "@styles/main.scss";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

//#endregion [i18n]

//#region [Styles]
import "@styles/pages/team.scss";
//#endregion [Styles]

//#region [Built-in components]
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import TeamCard from "@components/TeamCard.astro";
//#endregion [Components]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import FranciscoImg from "@assets/images/team/francisco.jpg";
import IanImg from "@assets/images/team/ian.jpg";
import EvansImg from "@assets/images/team/evans.jpg";
import ProcampusImg from "@assets/images/team/procampus.png";
import GlebImg from "@assets/images/team/gleb.jpg";
import VictorImg from "@assets/images/team/victor.jpg";
import IvanImg from "@assets/images/team/ivan.jpg";
import MishaImg from "@assets/images/team/misha.jpg";
import OlgaImg from "@assets/images/team/olga.jpg";
import SergeyImg from "@assets/images/team/sergey.jpg";
import CodersImg from "@assets/images/team/coders.svg";
//#endregion [Images]

//#region [Videos]
//#endregion [Videos]
---

<Layout>
  <!-- Header -->
  <section id="hero">
    <div class="container">
      <div class="hero">
        <h1 class="hero__title">
          {t("team.how_become_ambassador_title")}
        </h1>
        <h3 class="hero__subtitle">
          {t("team.how_become_ambassador_text")}
          <span class="hero__email-link-wrap" style="white-space: nowrap">
            <a
              class="hero__email-link highlight-link"
              href="mailto:<EMAIL>"><EMAIL></a
            >
            <span class="hero__copy-email" style="cursor: pointer">
              <!--prettier-ignore-->
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256"><path d="M216,32H88a8,8,0,0,0-8,8V80H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V176h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V96H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path></svg>
            </span>
          </span>
        </h3>
      </div>
    </div>
  </section>
  <!-- Ambassadors -->
  <section id="ambassadors" class="section-space">
    <div class="container">
      <div class="ambassadors">
        <h1 class="ambassadors__title">
          {t("team.ambassador_title")}
        </h1>
        <div class="ambassadors__cards team__cards-grid">
          <TeamCard
            name="Ian McClurg"
            title="Canada and UK"
            img={IanImg}
            link="mailto:<EMAIL>"
          />
        </div>
      </div>
    </div>
  </section>
  <!-- Management and operations -->
  <section id="management" class="section-space">
    <div class="container">
      <div class="management">
        <h1 class="management__title">{t("team.team_title")}</h1>
        <div class="management__cards team__cards-grid">
          <TeamCard
            name="Gleb Shaportov"
            title="CEO"
            img={GlebImg}
            link="https://www.linkedin.com/in/glebs1981/"
          />
          <TeamCard
            name="Victor Alekseev"
            title="CPO"
            img={VictorImg}
            link="https://www.linkedin.com/in/victor-alekseev-129a3066/"
          />
          <TeamCard
            name="Ivan Tershukov"
            title="CTO"
            img={IvanImg}
            link="https://www.linkedin.com/in/ivan-tershukov-7323babb/"
          />
          <TeamCard
            name="Mikhail Khlystalin"
            title="Design"
            img={MishaImg}
            link="https://www.linkedin.com/in/kobro/"
          />
          <TeamCard name="Olga Chistiakova" title="COO" img={OlgaImg} />
          <TeamCard
            name="Sergey Zyrianov"
            title="Motion design"
            img={SergeyImg}
          />
          <TeamCard
            name="10+ specialists"
            title="Development team"
            img={CodersImg}
          />
        </div>
      </div>
    </div>
  </section>
</Layout>

<script>
  import gsap from "gsap";

  const copyEmailIcon = document.querySelector(".hero__copy-email");

  if (copyEmailIcon) {
    const email = "<EMAIL>";
    async function copyToClipboard(text: string) {
      // Check if Clipboard API is supported
      if (!navigator.clipboard) {
        throw new Error("Clipboard API not supported");
      }
      await navigator.clipboard.writeText(text);
    }

    function animateCopyIcon() {
      gsap
        .timeline({
          paused: true,
        })
        .to(copyEmailIcon, {
          scale: 0.5,
          duration: 0.3,
          ease: "Power2.easeInOut",
        })
        .to(copyEmailIcon, {
          scale: 1,
          duration: 1,
          ease: "Elastic.easeOut",
        })
        .play();
    }

    copyEmailIcon.addEventListener("click", () => {
      copyToClipboard(email);
      animateCopyIcon();
    });
  }
</script>
