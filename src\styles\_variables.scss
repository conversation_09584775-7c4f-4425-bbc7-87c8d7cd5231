$white: #fff;
$black: #181b1e;

$primary: #ffe83f;
$secondary: #e4a79d;
$accent: #9100ff;
$blue: #0089ff;

$gray-light: #f4f5f9;
$gray: #dfe5ea;

// Easing
$smooth-ease: cubic-bezier(0.645, 0.045, 0.355, 1);

// Spacing
$space-128: 8rem;
$space-88: 5.5rem;
$space-80: 5rem;
$space-64: 4rem;
$space-56: 3.5rem;
$space-48: 3rem;
$space-32: 2rem;
$space-24: 1.5rem;
$space-16: 1rem;
$space-8: 0.5rem;

// Border radius
$main-radius: 0.5rem;

// Transition
@mixin transition-colors {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  transition-duration: 300ms;
}

@mixin transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  transition-duration: 300ms;
}

@mixin transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  transition-duration: 300ms;
}

@mixin transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  transition-duration: 300ms;
}
