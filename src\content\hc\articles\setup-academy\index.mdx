---
title: Setup Academy
date: 2024-03-08T12:00:00.000Z
cover: "./_images/cover.jpg"
---

import { Image, Picture } from "astro:assets";

import dashboard1 from "./_images/academy-dashboard-01-min.jpg";
import dashboard2 from "./_images/academy-dashboard-02-min.jpg";

import junicoach1 from "./_images/junicoach-app-01-min.jpg";
import junicoach2 from "./_images/junicoach-app-02-min.jpg";
import junicoach3 from "./_images/junicoach-app-03-min.jpg";

import settings1 from "./_images/academy-settings-01-min.jpg";
import settings2 from "./_images/academy-settings-02-min.jpg";

<h1 class="headline">How to set up your Academy account</h1>
<p>
  Create Players in your Academy account and connect them with representatives
  using the invitation link
</p>
<h2>Using Web Dashboard</h2>
<p>
  Invite the player representative. They will receive an email with an
  invitation to join as a player representative.
</p>
<figure class="image-wrap">
  <Picture src={dashboard1} alt="" format="webp" />
</figure>
<figure class="image-wrap">
  <Picture src={dashboard2} alt="" format="webp" />
</figure>
<h2>Using JuniCoach App</h2>
<figure class="image-wrap cols-3">
  <Image src={junicoach1} alt="" format="webp" />
  <Image src={junicoach2} alt="" format="webp" />
  <Image src={junicoach3} alt="" format="webp" />
</figure>
<h2>Get new players and representatives using your website</h2>
<p>
  Put this link into your web-site. New users will register and automatically
  appear in your Academy account.
</p>
<p>
  Using the academy link, representatives create an account for themselves and
  their player. These accounts are automatically linked.
</p>
<figure class="image-wrap cols-3">
  <Image src={settings1} alt="" format="webp" />
</figure>
<h2>Hide tariffs for representatives</h2>
<p>
  Hide original tariffs for representatives if you use your customizable pricing
  for conducting tests and performance analysis
</p>
<figure class="image-wrap cols-3">
  <Image src={settings2} alt="" format="webp" />
</figure>
