@use "sass:color";
@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Hero]*/
.hero {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  margin-top: $space-48;
}

.hero__title {
  width: 90%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.hero__subtitle {
  max-width: 52rem;
  margin-top: $space-24;
}

.hero__button {
  margin-top: $space-32;
}

.button--mod2 > * {
  font-weight: 700;
}

.hero__cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $space-64;
  margin-top: $space-64;

  @media screen and (max-width: $tablet) {
    gap: $space-32;
  }
  @media screen and (max-width: $mobile) {
    gap: $space-24;
    grid-template-columns: repeat(2, 1fr);
  }
}

.hero__card {
  @media screen and (max-width: $mobile) {
    &:not(:nth-child(-n + 2)) {
      display: none;
    }
  }
}
/*#endregion [Hero]*/

/*#region [Player example]*/

.player-example__title {
  margin-bottom: $space-48;
}

// Profile
.player-example__profile-wrap {
  display: grid;
  grid-template-columns: 0.3fr 1fr;
  gap: $space-32;
  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.player-example__card-button {
  margin-top: $space-16;
}

.player-example__skills-grid {
  display: grid;
  gap: $space-8 0;
  grid-template-areas:
    "b c"
    "d e";

  @media screen and (max-width: $mobile) {
    display: flex;
    gap: initial;
  }
}

.player-example__skills-slider {
  overflow: visible;

  @media screen and (max-width: $mobile) {
    display: block;
    flex-direction: column;
  }
}

.player-example__skills-slide {
  @media screen and (max-width: $mobile) {
    width: 90%;
  }
}

/*#endregion [Player example]*/

/*#region [Metrics]*/
#metrics {
  overflow: visible;
}

.metrics__content {
  position: relative;
  display: flex;
  align-items: center;
}

.metrics__title {
  max-width: 780px;
  margin-bottom: $space-48;
}

.metrics__image-wrap {
  display: block;
  width: 80%;
  border-radius: $space-24;
  overflow: hidden;

  @media screen and (max-width: $mobile) {
    border-radius: $space-8;
    width: 500px;

    & img {
      width: 200%;
      max-width: none;
    }
  }
}

.metrics__video-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  overflow: hidden;
  isolation: isolate;
  height: 90%;
  border-radius: $space-24;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;

  @media screen and (max-width: $mobile) {
    margin-top: 30%;
    width: 70%;
    height: 80%;
    border-radius: $space-8;
  }

  & video {
    height: 100%;
    transform: scale(1.5);

    @media screen and (max-width: $mobile) {
      width: 100%;
      height: auto;
      margin-top: -50%;
    }
  }
}

.video__play-button {
  display: none;
  width: 80px;
  height: 80px;
  position: absolute;
  z-index: 15;
  pointer-events: none;
}
/*#endregion [Metrics]*/

/*#region [Track]*/
.track__title {
  @media screen and (max-width: $mobile) {
    margin-bottom: $space-8;
  }
}

.track__header-cols {
  display: flex;
  column-gap: $space-128;
  row-gap: $space-32;
  justify-content: space-between;
  margin-top: $space-48;
  margin-bottom: $space-48;

  @media screen and (max-width: $mobile) {
    flex-wrap: wrap;
    row-gap: $space-16;
    margin-top: $space-32;
    margin-bottom: $space-32;
  }
}

.track__compare-wrap {
  @media screen and (min-width: $tablet) {
    padding: 0px $space-24;
    max-width: 1360px;
    margin: 0 auto;
  }

  @media screen and (max-width: $mobile) {
    width: 100%;
    overflow-y: scroll;
  }
}
.track__compare-image {
  @media screen and (max-width: $mobile) {
    display: block;
    width: 250%;
    max-width: none;
  }
}
/*#endregion [Track]*/

/*#region [Start]*/
.start__header {
  display: flex;
  justify-content: space-between;
  gap: $space-32;

  @media screen and (max-width: $mobile) {
    flex-direction: column;
  }
}

.start__subtitle {
  margin-top: $space-8;
  max-width: 36.13rem;
}

.start__price-col {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.start__price-title {
  margin-bottom: $space-8;
}

.start__price-wrap {
  display: flex;
  gap: $space-16;
}

.start__price-block {
  padding: $space-16;
  background-color: $primary;
  border-radius: $space-8;
}

.start__steps {
  margin-top: $space-48;
  display: grid;
  gap: $space-80;
  grid-template-columns: repeat(3, 1fr);

  @media screen and (max-width: $mobile) {
    grid-template-columns: 1fr;
    gap: $space-32;
  }
}

.start__step-circe {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 5rem;
  height: 5rem;
  background-color: $primary;
  border-radius: 100%;
  font-size: 2rem;
  font-weight: 700;
}

.start__step-text {
  margin-top: $space-16;
}

/*#endregion [Start]*/

/*#region [Footer cards]*/
.footer-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    display: flex;
    flex-direction: column;
  }
}

.footer-cards__card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: $space-24;
  text-align: center;
  gap: $space-24;

  background-color: $white;
  border-radius: $space-8;
  border: 1px solid $gray;

  @include transition-colors;

  &:not(div):hover {
    background-color: color.change($primary, $lightness: 87%);
  }

  &.is--material {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    @media screen and (max-width: $mobile) {
      flex-direction: column-reverse;
      align-items: flex-start;
    }
  }

  &.is--accent {
    background-color: $primary;
  }
}

.footer-cards__card-side {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: $space-8;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-text {
  width: 80%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.footer-cards__card-icon {
  width: $space-64;
  height: $space-64;
}

.footer-cards__social-links {
  display: flex;
  gap: $space-16;
}

.footer-cards__social-icon {
  width: 2rem;
  height: 2rem;

  @include transition-all;

  &:hover {
    transform: scale(1.2);
  }
}
/*#endregion [Footer cards]*/
