---
import "@styles/main.scss";
import Layout from "@layouts/Layout.astro";
import fs from "node:fs/promises";
// Get list of all pages in pages folder using fs nodejs
const pagesEn = await fs.readdir("src/pages").then((pages) => {
  return pages
    .filter((page) => page.endsWith(".astro"))
    .map((page) => "/" + page.replace(".astro", ""));
});

const pagesRu = await fs.readdir("src/pages/ru").then((pages) => {
  return pages
    .filter((page) => page.endsWith(".astro"))
    .map((page) => "/ru/" + page.replace(".astro", ""));
});

// const pagesEn = async () => {
//   console.log(await import.meta.glob("../pages/*.astro"));
// };

// console.log("pages :>> ", pagesEn());

// console.log(Astro.params);
---

<Layout title="All Pages" noindex={true}>
  <section style="margin-top: 48px">
    <div class="container">
      <h2>English</h2>
      <ul>
        {
          pagesEn.map((page) => (
            <li>
              <a href={page} target="_blank">
                {page}
              </a>
            </li>
          ))
        }
      </ul>
      <br />
      <br />
      <h2>Русский</h2>
      <ul>
        {
          pagesRu.map((page) => (
            <li>
              <a href={page} target="_blank">
                {page}
              </a>
            </li>
          ))
        }
      </ul>
    </div>
  </section>
</Layout>
