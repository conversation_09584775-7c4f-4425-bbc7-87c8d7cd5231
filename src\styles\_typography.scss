@use "screens" as screen;

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Regular.woff2") format("woff2");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Medium.woff2") format("woff2");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-SemiBold.woff2") format("woff2");
  font-weight: 600;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Bold.woff2") format("woff2");
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}

h1,
.h1 {
  font-size: 72px;
  font-weight: 800;
  line-height: 95%;
}

h2,
.h2 {
  font-size: 48px;
  line-height: 104%;
  font-weight: 800;
}

h3,
.h3 {
  font-size: 32px;
  font-weight: 400;
  line-height: 110%;
}

h4,
.h4 {
  font-size: 26px;
  font-weight: 400;
  line-height: 120%;
}

h5,
.h5 {
  font-size: 24px;
  font-weight: 400;
  line-height: 120%;
}

p,
.p {
  font-size: 21px;
  font-weight: 400;
  line-height: 130%;
}

.p2 {
  font-size: 1rem;
}

.p3 {
  font-size: 0.88rem;
}

@media screen and (max-width: 992px) {
  h1,
  .h1 {
    font-size: 40px;
    font-weight: 700;
    line-height: 100%;
  }

  h2,
  .h2 {
    font-size: 31px;
    font-weight: 700;
  }

  h3,
  .h3 {
    font-size: 21px;
  }

  h4,
  .h4 {
    font-size: 18px;
    font-weight: 400;
  }

  h5,
  .h5 {
    font-size: 21px;
    font-weight: 400;
    line-height: 120%;
  }

  p,
  .p {
    font-size: 18px;
    font-weight: 400;
  }

  .p2 {
    font-size: 18px;
    font-weight: 400;
  }
}
