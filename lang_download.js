import { LokaliseApi } from "@lokalise/node-api";
import fs from "fs";
import path from "path";
import https from "https";
import unzipper from "unzipper";

const apiKey = "8f22abf78d39e03055d597e6ea31991f1e30a430";
const project_id = "138866886417552d55c261.90303764";

const lokaliseApi = new LokaliseApi({ apiKey });

const localURL = await lokaliseApi
  .files()
  .download(project_id, { format: "json", original_filenames: true });

await downloadAndUnzip(localURL.bundle_url);

async function downloadAndUnzip(url) {
  const file = fs.createWriteStream("file.zip");
  const request = https.get(url, function (response) {
    response.pipe(file);
    file.on("finish", function () {
      file.close(async () => {
        await unzipper.Open.file("file.zip").then((d) =>
          d.extract({ path: "src/lang" })
        );
        fs.unlinkSync("file.zip");
        moveFiles();
      });
    });
  });
}

function moveFiles() {
  const subfolders = ["en", "ru", "es", "pt"];
  subfolders.forEach((subfolder) => {
    const dir = path.join("src/lang/", subfolder);
    fs.readdirSync(dir).forEach((file) => {
      const oldPath = path.join(dir, file);
      const newPath = path.join("src/lang", file);
      fs.renameSync(oldPath, newPath);
    });
    fs.rmdirSync(dir);
  });
}
