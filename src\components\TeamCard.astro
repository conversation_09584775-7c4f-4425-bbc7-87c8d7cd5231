---
import type { HTMLTag } from "astro/types";
import type { ImageMetadata } from "astro";
import { Image } from "astro:assets";

interface Props {
  name: string;
  title: string;
  img: ImageMetadata;
  link?: string;
  class?: string;
}

const { name, title, img, link, class: ClassList } = Astro.props;

const Tag: HTMLTag = link ? "a" : "div";
const setTarget = link ? "_blank" : null;
---

<Tag
  class:list={["ambassadors__card", ClassList]}
  href={link}
  target={setTarget}
  data-name={name}
>
  <Image class="ambassadors__card-avatar" src={img} alt={name} />
  <h3 class="ambassadors__card-name p">{name}</h3>
  <p class="ambassadors__card-title p2">{title}</p>
</Tag>

<style lang="scss">
  @use "@styles/variables" as *;
  @use "@styles/screens" as *;

  .ambassadors__card {
    display: block;

    @include transition-opacity;

    &:hover:not(div) {
      opacity: 0.7;
    }

    &[div] {
      cursor: default;
    }
  }
  .ambassadors__card-avatar {
    display: block;
    width: 100%;
    overflow: hidden;
    border-radius: $space-16;
  }
  .ambassadors__card-name {
    margin-top: $space-8;
  }
</style>

<script>
  import { track } from "@amplitude/analytics-browser";

  const cards = document.querySelectorAll(
    ".ambassadors__card"
  ) as NodeListOf<HTMLElement>;

  cards.forEach((card) => {
    card.addEventListener("click", () => {
      track("Click team profile", {
        link: card.dataset.name,
      });
    });
  });
</script>
