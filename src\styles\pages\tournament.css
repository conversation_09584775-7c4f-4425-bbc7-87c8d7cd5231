/*#region [Font]*/
@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Regular.woff2") format("woff2");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Medium.woff2") format("woff2");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: "Onest";
  src: url("/fonts/Onest-Bold.woff2") format("woff2");
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}

html {
  font-size: 1vw;
}

@media screen and (min-width: 1440px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: "Onest", Inter, Arial, Helvetica, sans-serif;
  line-height: 140%;
  color: #292f32;
}

h1,
.h1 {
  font-weight: 700;
  font-size: 5rem;
}
@media screen and (max-width: 688px) {
  h1,
  .h1 {
    font-size: 12rem;
  }
}

h2,
.h2 {
  font-weight: 700;
  font-size: 3.47rem;
}
@media screen and (max-width: 992px) {
  h2,
  .h2 {
    font-size: 40px;
  }
}

h3,
.h3 {
  font-weight: 500;
  font-size: 2.22rem;
}
@media screen and (max-width: 992px) {
  h3,
  .h3 {
    font-size: 30px;
  }
}

h4,
.h4 {
  font-size: 24px;
}
@media screen and (max-width: 688px) {
  h4,
  .h4 {
    font-size: 21px;
  }
}

p,
.p {
  font-size: 21px;
}

.p-small {
  font-size: 16px;
}
/*#endregion [Font]*/

/*#region [Common]*/

.container {
  max-width: 88.89rem;
}
@media screen and (max-width: 688px) {
  .container {
    max-width: 100%;
    padding-left: 16px;
    padding-right: 16px;
  }
}

section {
  overflow: unset;
}

ul {
  padding: 0;
}

.overflow-hidden {
  overflow: hidden;
}

@media screen and (max-width: 688px) {
  .hide-on-mobile {
    display: none;
  }
}

@media screen and (min-width: 688px) {
  .hide-on-desktop {
    display: none;
  }
}

@media screen and (max-width: 688px) {
  .section-space {
    margin-top: 19.2rem;
  }
}

.section-headline {
  margin-bottom: 48px;
}

.card {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #dedede;
  background: #fff;
}

.text-link {
  color: #0d99ff;
  text-decoration: underline;
}

@media (hover: hover), (-moz-touch-enabled: 0), (pointer: fine) {
  .text-link:hover {
    color: #0e5d96;
  }
}

/*#endregion [Common]*/

/*#region [Header]*/
header {
  margin-top: 64px;
}

@media screen and (max-width: 992px) {
  header {
    margin-top: 32px;
  }
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

@media screen and (max-width: 688px) {
  .header {
    align-items: flex-start;
    flex-direction: column;
  }
}

.header__description {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
}

.header__description > p {
  margin-top: 8px;
  font-size: 14px;
  text-align: end;
}

@media screen and (max-width: 992px) {
  .header__description {
    align-items: flex-start;
    margin-top: 16px;
    align-items: center;
    flex-direction: row;
    text-align: start;
  }

  .header__description > p {
    text-align: start;
    margin-top: 0px;
    margin-left: 8px;
  }

  .header__description > svg {
    max-width: 220px;
  }
}

/*#endregion [Header]*/

/*#region [Hero]*/
.hero {
  margin-top: 48px;
}

.hero__subtitle {
  font-size: 40px;
  font-weight: 500;
}

@media screen and (max-width: 992px) {
  .hero__subtitle {
    font-size: 32px;
  }
}

.hero__headline {
  margin-top: 24px;
}

@media screen and (max-width: 992px) {
  .hero__headline {
    font-size: 40px;
  }
}

.hero__description {
  margin-top: 16px;
  font-size: 24px;
  max-width: 880px;
}

.hero__button {
  margin-top: 24px;
}

@media screen and (max-width: 992px) {
  .hero__button {
    width: 100%;
  }
}

.hero__cards {
  margin-top: 48px;
  display: flex;
  flex-direction: row;
}

@media screen and (max-width: 992px) {
  .hero__cards {
    flex-direction: column;
  }
}

.hero__card {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px;
  width: 100%;
  font-size: 24px;
}

@media screen and (max-width: 992px) {
  .hero__card {
    flex-direction: column;
    align-items: flex-start;
  }
}

.hero__card-icons {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

@media screen and (max-width: 992px) {
  .hero__card-icons > svg {
    margin-bottom: 16px;
    width: 48px;
    height: auto;
  }
}

.hero__cards > * + * {
  margin-left: 16px;
}

@media screen and (max-width: 992px) {
  .hero__cards > * + * {
    margin-top: 16px;
    margin-left: 0;
  }
}

.hero__photos {
  margin-top: 24px;
  display: flex;
}

@media screen and (max-width: 992px) {
  .hero__photos {
    flex-direction: column;
  }
}

.hero__photo-wrap {
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.hero__photos > * + * {
  margin-left: 16px;
}

@media screen and (max-width: 992px) {
  .hero__photos > * + * {
    margin-top: 16px;
    margin-left: 0px;
  }
}

/*#endregion [Hero]*/

/*#region [Goals]*/
.goals__cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

@media screen and (max-width: 992px) {
  .goals__cards {
    grid-template-columns: repeat(1, 1fr);
  }
}

.goals__card {
  padding: 24px;
}

.goals__card-text {
  margin-top: 16px;
  font-size: 24px;
}

.goals__register-button {
  width: 100%;
}
/*#endregion [Goals]*/

/*#region [Benefits]*/
.benefits__headline {
  max-width: 900px;
}

.benefits__content {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-top: 48px;
}
@media screen and (max-width: 992px) {
  .benefits__content {
    display: block;
  }
}

.benefits__cards {
  display: flex;
  flex-direction: column;
  flex: 0 0 50%;
}

.benefits__cards > *:not(h2) + * {
  margin-top: 24px;
}

.benefits__card {
  display: flex;
  padding: 24px 144px 24px 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
  position: relative;
  border-radius: 12px;
  background: #fff;
  overflow: hidden;
}
@media screen and (max-width: 992px) {
  .benefits__card {
    padding: 16px;
    gap: 24px;
  }
}

.benefits__card-icon {
  position: absolute;
  top: 10%;
  right: 0;
  transform: rotateZ(15deg);
}
@media screen and (max-width: 992px) {
  .benefits__card-icon {
    position: static;
    transform: none;
    width: 88px;
    height: auto;
  }
}

.benefits__button {
  width: 100%;
}

@media screen and (max-width: 992px) {
  .benefits__content > picture {
    display: none;
  }

  picture + .benefits__card-icon {
    display: none;
  }
}

@media screen and (min-width: 992px) {
  .benefits__players-cards.is--mobile {
    display: none;
  }
}

/*#endregion [Benefits]*/

/*#region [Conditions]*/
.conditions__cols {
  display: flex;
  flex-direction: row;
}

@media screen and (max-width: 992px) {
  .conditions__cols {
    flex-direction: column;
  }
}

.conditions__cols > * + * {
  margin-left: 48px;
}

.conditions__col {
  width: 100%;
}

/* Cards */
.conditions__col.is--cards > * + * {
  margin-top: 16px;
}

.conditions__card {
  display: flex;
  flex-direction: row;
  padding: 16px;
}

.conditions__card:nth-child(2) {
  align-items: center;
}

.conditions__card > svg {
  flex-shrink: 0;
  margin-right: 16px;
}

@media screen and (max-width: 992px) {
  .conditions__card {
    flex-direction: column;
  }

  .conditions__card:nth-child(2) {
    align-items: flex-start;
  }

  .conditions__card > svg {
    margin-bottom: 16px;
  }
}

.conditions__card-subtext {
  color: #65696c;
  margin-top: 8px;
}

/* Tags */
.conditions__col.is--tags {
  position: relative;
  max-width: 590px;
}

@media screen and (max-width: 992px) {
  .conditions__col.is--tags {
    display: none;
  }
}

.conditions__tag {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1000px;
  background: #ffe83f;
  border: 1px solid #decc47;
  padding: 16px 24px;
  margin-top: 24px;
}

.conditions__tag:nth-child(1) {
  transform: rotateZ(25deg);
  margin-left: auto;
}

.conditions__tag:nth-child(2) {
  margin-top: 8px;
  transform: rotateZ(-15deg);
}

.conditions__tag:nth-child(3) {
  transform: rotateZ(10deg);
  margin-left: auto;
}

.conditions__tag:nth-child(5) {
  transform: rotateZ(-6deg);
  margin-left: auto;
}

.conditions__tag:nth-child(6) {
  margin-top: 48px;
  transform: rotateZ(7deg);
}

.conditions__tag > * {
  font-size: 24px;
}

/*#endregion [Conditions]*/

/*#region [Participate]*/
.participation__cols {
  display: flex;
  flex-direction: row;
}

@media screen and (max-width: 992px) {
  .participation__cols {
    flex-direction: column;
  }
}

.participation__cols > * + * {
  margin-left: 48px;
}

@media screen and (max-width: 992px) {
  .participation__cols > * + * {
    margin-left: 0;
  }
}

.participation__col.is--cards > * + * {
  margin-top: 16px;
}

.participation__col {
  width: 100%;
}

/* Cards */
.participation__col.is--cards {
  padding: 0;
}

.participation__card {
  display: flex;
  padding: 16px 24px;
}

.participation__card-number {
  font-size: 50px;
  line-height: 100%;
  font-weight: 700;
}

.participation__card-text {
  margin-left: 24px;
}

@media screen and (max-width: 992px) {
  .participation__card {
    flex-direction: column;
  }

  .participation__card-text {
    margin-top: 16px;
    margin-left: 0;
  }
}

/* Form */
.participation__col.is--form {
  padding: 24px;
  max-width: 590px;
}

.participation__col.is--form > * + * {
  margin-top: 24px;
}

@media screen and (max-width: 992px) {
  .participation__col.is--form {
    margin-top: 24px;
    margin-left: auto;
    margin-right: auto;
    padding: 16px;
  }
}

.participation__input {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #dedede;
  background: #fff;
  padding: 16px 24px;
}

.participation__checks {
  margin-top: 16px;
  margin-bottom: 16px;
}

.participation__check-wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.participation__check-wrap:last-of-type {
  margin-top: 8px;
}

@media screen and (max-width: 992px) {
  .participation__check-wrap {
    line-height: 110%;
  }

  .participation__check-wrap:last-of-type {
    margin-top: 12px;
  }
}

input[type="checkbox"] {
  display: grid;
  appearance: none;
  flex-shrink: 0;
  margin-right: 12px;
  width: 1.15em;
  height: 1.15em;
  border-radius: 0.15em;
  place-content: center;
  background-color: white;
  border: none !important;
  outline: 2px solid #2ed887;

  transition: background-color 120ms ease-in-out;
}

input[type="checkbox"]:checked {
  background-color: #2ed887;
}

input[type="checkbox"]:focus {
  outline: 2px solid black;
}

input[type="checkbox"]::before {
  content: "";
  width: 0.65em;
  height: 0.65em;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em white;
  clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
}

input[type="checkbox"]:checked::before {
  transform: scale(1);
}

.participation__button {
  width: 100%;
  margin-top: 48px;
}

.participation__success {
  padding: 16px;
  background-color: #20ad69;
  border-radius: 8px;
  color: white;
  display: none;
}

.participation__error {
  padding: 16px;
  background-color: #931522;
  border-radius: 8px;
  color: white;
  display: none;
}

/*#endregion [Participate]*/

/*#region [Rules]*/
.rules__cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

@media screen and (max-width: 992px) {
  .rules__cards {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .rules__cards > * + * {
    margin-top: 16px;
  }

  .rules__card-title {
    font-size: 32px;
  }
}

.rules__card {
  padding: 16px 24px;
}

@media screen and (max-width: 1280px) {
  .rules__cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

.rules__card {
  padding: 16px 24px;
}

.rules__card.is--big {
  grid-column: span 2;
}

.rules__card.is--big > .rulse__card-text {
  margin-top: 16px;
}

@media screen and (max-width: 992px) {
  .rules__card > .rulse__card-text {
    margin-top: 8px;
  }
}

.rulse__card-text {
  margin-top: 24px;
}

.rules__card-subtitle {
  margin-top: 8px;
  font-weight: 500;
}

@media screen and (max-width: 992px) {
  .rules__card-subtitle {
    font-size: 24px;
  }
}
/*#endregion [Rules]*/

/*#region [Info]*/
.info__cards {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
}

@media screen and (max-width: 992px) {
  .info__cards {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .info__cards > * + * {
    margin-top: 16px;
  }
}

.info__card.is--big {
  grid-column: span 3;
  display: flex;
  align-items: center;
  flex-direction: row;
}

@media screen and (max-width: 992px) {
  .info__card.is--big {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 16px;
  }
}

.info__card-text.is--big {
  font-size: 24px;
  margin-left: 16px;
}

@media screen and (max-width: 992px) {
  .info__card-text.is--big {
    margin-left: 0;
  }
}

.info__card {
  padding: 16px 24px;
  grid-column: span 2;
}

.info__swiper {
  grid-column: span 6;
}

.info__swiper {
  overflow: visible;
  width: 100%;
}

.swiper-wrapper {
  height: auto;
}

.swiper-slide.is--info {
  border-radius: 8px;
  overflow: hidden;
}

@media screen and (max-width: 992px) {
  .swiper-slide.is--info {
    width: 90%;
  }
}

.info__slide-img-wrap > img {
  transition: transform 0.3s var(--smooth-ease);
}

.info__slide-img-wrap:hover > img {
  transform: scale(1.05);
}

.swiper-scrollbar {
  position: relative !important;
  margin-top: 16px;
}

.info__button {
  text-align: center;
  margin-top: 16px;
  margin-left: auto;
  margin-right: auto;
}

@media screen and (max-width: 992px) {
  .info__button {
    text-align: start;
    width: 100%;
  }
}

/*#endregion [Info]*/

/*#region [FAQ]*/
.faq__cols {
  display: flex;
}

@media screen and (max-width: 992px) {
  .faq__cols {
    display: flex;
    flex-direction: column;
  }
}

.faq__col {
  width: 100%;
}

.faq__col.is--contacts {
  align-self: flex-start;
  margin-left: 72px;
  max-width: 590px;
  padding: 24px;
  position: sticky;
  top: 32px;
}

@media screen and (max-width: 992px) {
  .faq__col.is--contacts {
    margin-top: 24px;
    margin-left: 0;
    max-width: 590px;
    padding: 24px;
  }
}

.faq__contacts-text {
  margin-top: 24px;
}

.faq__contacts-text > * + * {
  display: inline-block;
  margin-top: 8px;
}

.faq__list > * + * {
  margin-top: 32px;
}

.faq__title {
  font-size: 24px;
  font-weight: 600;
}

.faq__content {
  margin-top: 8px;
}
/*#endregion [FAQ]*/

.footer {
  display: flex;
  margin-top: 32px;
  margin-bottom: 80px;
  justify-content: space-between;
  flex-wrap: wrap;
}

.footer p {
  font-size: 16px;
}
