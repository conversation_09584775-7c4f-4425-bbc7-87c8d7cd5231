---
interface Props {
  propLabel: string;
  value: string;
  benchmarkValue: string;
  playerBarPosition: number;
  average: number;
  lang?: "ru" | "en";
}

const { propLabel, value, benchmarkValue, playerBarPosition, average, lang } =
  Astro.props;
---

<div class="result">
  <div class="result__info">
    <div class="result__value-wrap is--player">
      <div class="result__name">{propLabel}</div>
      <div class="result__value">{value}</div>
    </div>
    <div class="result__value-wrap is--banchmark">
      <div class="result__name">{lang === "ru" ? "Эталон" : "Benchmark"}</div>
      <div class="result__value">{benchmarkValue}</div>
    </div>
  </div>
  <div class="result__bar">
    <div class="result__bar-pointer" style={`left:${average ?? 0}%`}>
      <div class="result__bar-dot"></div>
      <div class="result__bar-dot-label">
        {lang === "ru" ? "среднее" : "average"}
      </div>
    </div>
    <div class="result__meter-wrap">
      <div
        class="result__meter-value-line"
        style={`width: ${playerBarPosition ?? 0}%`}
      >
      </div>
    </div>
  </div>
</div>

<style>
  .result {
    padding: 8px 8px 0px;
  }

  .result__info {
    display: flex;
    column-gap: 32px;
    line-height: 120%;
    font-size: 15px;
    justify-content: space-between;
  }

  .result__value-wrap.is--banchmark {
    opacity: 0.7;
    text-align: end;
  }

  .result__value {
    font-weight: 600;
  }

  .result__bar {
    position: relative;
  }

  .result__bar-pointer {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;
    top: -4px;
  }

  .result__bar-dot {
    height: 12px;
    width: 4px;

    background-color: #6d839c;
    border-radius: 2px;
    margin: 0;
  }

  .result__bar-dot-label {
    font-size: 12px;
    color: rgba(67, 73, 80, 0.8);
  }

  .result__meter-wrap {
    position: relative;
    z-index: 2;
    width: 100%;
    background: #a3b1c2;
    border: none;
    height: 6px;
    margin-top: 8px;
    margin-bottom: 4px;
  }

  .result__meter-value-line {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    background: var(--primary);
  }
</style>
