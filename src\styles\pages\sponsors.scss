@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Hero]*/
.hero {
  margin-top: $space-48;
}

.hero {
  display: grid;
  grid-template-columns: 0.4fr 1fr;
  gap: $space-64;

  @media screen and (max-width: $mobile) {
    grid-template-columns: 1fr;
    gap: $space-32;
  }
}

.hero__col {
  overflow: hidden;
}

.hero__video-tag {
  width: 100%;
}

.hero__title {
  width: 90%;

  @media screen and (max-width: $mobile) {
    width: 100%;
  }
}

.hero__subtitle {
  margin-top: $space-24;
  max-width: 680px;
  line-height: 140%;
}

.hero__contacts {
  margin-top: $space-48;
}

/*#endregion [Hero]*/
