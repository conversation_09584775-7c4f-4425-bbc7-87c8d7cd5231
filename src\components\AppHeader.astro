---
//#region [Built-in components]
import { Image } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import LanguagePicker from "@components/LanguagePicker.astro";
import AppButton from "@components/AppButton.astro";
import AppHeaderLink from "@components/AppHeaderLink.astro";

//#endregion [Components]

//#region [Images]
import jsLogo from "@assets/images/js-jc-logo.svg";
//#endregion [Images]

//#region [i18n]
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "../lang/utils";

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]
---

<header class="container">
  <div class="header">
    <div class="header__top">
      <a class="header__logo-wrap" href={translatePath("/")}>
        <Image class="header__logo" src={jsLogo} alt="JuniStat" />
        <p class="header__logo-text p3">
          Identify and develop young talents with AI‑powered mobile applications
        </p>
      </a>
      <div class="header__buttons-wrap">
        <LanguagePicker class="header__lang-selector p2" />

        <AppButton
          as="a"
          typeStyle="primary"
          id="sign-up"
          href={lang === 'ru' ? "https://app.junistat.ru/welcome?utm_source=website&utm_medium=header-button&utm_campaign=website":"https://app.junistat.com/welcome?utm_source=website&utm_medium=header-button&utm_campaign=website"}
          target="_blank"
        >
          {t("sign_up")}
        </AppButton>

        <!-- Mobile menu trigger -->
        <button class="header__burger" aria-label="Menu toggle">
          <div class="header__burger-line is--1"></div>
          <div class="header__burger-line is--2"></div>
        </button>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar p2">
      <div class="navbar__links-wrap">
        <AppHeaderLink link="/clubs">{t("navigation.clubs")}</AppHeaderLink>
        <AppHeaderLink link="/parents">{t("navigation.parents")}</AppHeaderLink>
        <AppHeaderLink link="/">{t("navigation.rating")}</AppHeaderLink>
        {
          lang !== "ru" && (
            <AppHeaderLink link="/scouting">
              {t("navigation.scouting")}
            </AppHeaderLink>
          )
        }
        <AppHeaderLink link="/sponsors">
          {t("navigation.sponsors")}
        </AppHeaderLink>
        <AppHeaderLink link="/team">{t("navigation.team")}</AppHeaderLink>
        <AppHeaderLink link="/faq">{t("navigation.faq")}</AppHeaderLink>
        <AppHeaderLink link="/blog">{t("blog.title")}</AppHeaderLink>
        <AppHeaderLink link="/about">{t("navigation.about")}</AppHeaderLink>
      </div>
    </nav>
  </div>
</header>

<script>
  import { track } from "@amplitude/analytics-browser";

  import gsap from "gsap";
  import debounce from "@src/utils/debounce";

  //#region [Navbar Open/Close]
  const mobileMenuTrigger = document.querySelector(
    ".header__burger",
  ) as HTMLElement;

  const navbar = document.querySelector(".navbar") as HTMLElement;

  if (mobileMenuTrigger && navbar) {
    let menuOpened = false;
    mobileMenuTrigger.addEventListener("click", () => {
      if (!menuOpened) {
        openNavbar();
      } else {
        closeNavbar();
      }
    });

    const navbarAnim = gsap.to(navbar, {
      paused: true,
      height: "auto",
      duration: 0.5,
      ease: "power2.inOut",
    });

    function openNavbar(): void {
      navbarAnim.play();
      mobileMenuTrigger.classList.toggle("is--opened");
      menuOpened = true;
    }

    function closeNavbar(): void {
      navbarAnim.reverse();
      mobileMenuTrigger.classList.remove("is--opened");
      menuOpened = false;
    }

    const resetNavbarOnResize = debounce(() => {
      if (menuOpened) return;
      gsap.to(navbar, {
        height: "",
        duration: 0.5,
        ease: "power2.inOut",
      });
    }, 400);

    window.addEventListener("resize", resetNavbarOnResize);
  }

  //#endregion [Navbar Open/Close]

  //#region [Track Sign up]
  document.querySelector("#sign-up")?.addEventListener("click", () => {
    track("Click sign up");
  });
  //#endregion [Track Sign up]
</script>

<style lang="scss">
  @use "@styles/_variables.scss" as *;
  @use "@styles/_screens.scss" as *;

  header {
    width: 100%;
  }

  .header {
    position: relative;
    padding: $space-16 $space-16 $space-8;
    border-radius: 0px 0px 16px 16px;
    background-color: $white;
    z-index: 1000;
  }

  .header__top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    @media screen and (max-width: $mobile) {
      flex-direction: column;
    }
  }

  .header__logo-wrap {
    display: inline-block;
    max-width: 300px;
  }

  .header__logo {
    width: 100%;
  }

  .header__logo-text {
    margin-top: 0.3em;
  }

  .header__top .button {
    max-height: $space-48;
  }

  .header__burger {
    display: none;
    width: $space-48;
    height: $space-48;
    padding: 0.75rem $space-8;
    flex-shrink: 0;
    flex-direction: column;
    justify-content: space-between;

    @media screen and (max-width: $mobile) {
      display: flex;
      margin-left: auto;
    }

    &.is--opened {
      & > .header__burger-line {
        &.is--1 {
          transform: rotateZ(45deg);
          top: 0.649rem;
        }
        &.is--2 {
          transform: rotateZ(-45deg);
          top: -0.649rem;
        }
      }
    }
  }

  .header__burger-line {
    position: relative;
    top: 0;

    width: 100%;
    height: 2px;
    background-color: $black;

    @include transition-all;
  }

  .header__buttons-wrap {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    @media screen and (max-width: $mobile) {
      margin-top: $space-16;
    }
  }

  .header__lang-selector {
    border: none;
    margin-right: 1rem;
  }

  .navbar {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0.5rem;
    gap: 0rem 0.5rem;
    overflow: hidden;

    &::before {
      content: "";
      display: inline-block;
      margin-bottom: 0.5rem;
      width: 100%;
      height: 0.96px;
      background-color: $gray;
    }

    @media screen and (max-width: $mobile) {
      position: absolute;
      left: 0;
      z-index: 1000;
      width: 100%;
      height: 0;
      background-color: $white;
      border-radius: $space-16;
      margin-bottom: $space-16;
      box-shadow: 0px 16px 20px 0px rgba(0, 0, 0, 0.05);
    }
  }

  .navbar__links-wrap {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0.5rem;
    gap: 0rem 0.5rem;
    overflow: hidden;
    width: 100%;

    @media screen and (max-width: $mobile) {
      flex-direction: column;
      padding: 0 $space-8 $space-16;
    }
  }

  .navbar__link {
    padding: 0.5rem;
    border-radius: 8px;

    @include transition-colors;

    &:hover {
      background-color: $primary;
    }

    @media screen and (max-width: $mobile) {
      padding: 0.5rem 1rem;
    }
  }
</style>
