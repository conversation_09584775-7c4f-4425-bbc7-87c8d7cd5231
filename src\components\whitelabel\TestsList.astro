---
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

import TestCard from "@components/ai-test/TestCard.astro";

//#region [Images]
import BallImg from "@assets/images/ai-test/ball-fit.png";

// Tests icons
import Run15upIcon from "@assets/images/ai-test/tests-icons/15m-run-up.jpg";
import PushUpsIcon from "@assets/images/ai-test/tests-icons/push-ups.jpg";
import ArrowBallIcon from "@assets/images/ai-test/tests-icons/arrow-ball.jpg";
import ArrowIcon from "@assets/images/ai-test/tests-icons/arrow.jpg";
import Dribbling15Icon from "@assets/images/ai-test/tests-icons/dribbling15-ball.jpg";
import jumpIcon from "@assets/images/ai-test/tests-icons/jump-place.jpg";
import LadderFBIcon from "@assets/images/ai-test/tests-icons/ladder-front-back.jpg";
import LadderLRIcon from "@assets/images/ai-test/tests-icons/ladder-left-right.jpg";
import KickIcon from "@assets/images/ai-test/tests-icons/monster-kick-right.jpg";
import Run15Icon from "@assets/images/ai-test/tests-icons/run-15.jpg";
import SerpentIcon from "@assets/images/ai-test/tests-icons/serpient-ball.jpg";

// Posters
import Run15UpPoster from "@assets/images/ai-test/video-posters/poster_15mRunUp-min.jpg";
import Sprint15Poster from "@assets/images/ai-test/video-posters/poster_15mSprint-min.jpg";
import ArrowPoster from "@assets/images/ai-test/video-posters/poster_arrow-min.jpg";
import ArrowBallPoster from "@assets/images/ai-test/video-posters/poster_arrowBall-min.jpg";
import DribblingPoster from "@assets/images/ai-test/video-posters/poster_dribbling-min.jpg";
import JumpPoster from "@assets/images/ai-test/video-posters/poster_jump-min.jpg";
import KickPoster from "@assets/images/ai-test/video-posters/poster_kick-min.jpg";
import LadderBFPoster from "@assets/images/ai-test/video-posters/poster_ladderBF-min.jpg";
import LadderLRPoster from "@assets/images/ai-test/video-posters/poster_ladderLR-min.jpg";
import PushUpsPoster from "@assets/images/ai-test/video-posters/poster_pushups-min.jpg";
import SerpentPoster from "@assets/images/ai-test/video-posters/poster_serpent-min.jpg";

const posters = {
  Run15UpPoster: Run15UpPoster,
  Sprint15Poster: Sprint15Poster,
  ArrowPoster: ArrowPoster,
  ArrowBallPoster: ArrowBallPoster,
  DribblingPoster: DribblingPoster,
  JumpPoster: JumpPoster,
  KickPoster: KickPoster,
  LadderBFPoster: LadderBFPoster,
  LadderLRPoster: LadderLRPoster,
  PushUpsPoster: PushUpsPoster,
  SerpentPoster: SerpentPoster,
};

const optimizedPosters: any = {};
for (const [key, value] of Object.entries(posters)) {
  const optimizedPoster = await getImage({ src: value, format: "webp" });
  optimizedPosters[key] = optimizedPoster;
}

//#endregion [Images]

//#region [Videos]
import m15SprintMP4 from "@assets/videos/ai-tests/15m-sprint.mp4";
import m15SprintFromRunMP4 from "@assets/videos/ai-tests/15m-from-run-up.mp4";
import arrowWithBallMP4 from "@assets/videos/ai-tests/arrow-with-ball.mp4";
import arrowMP4 from "@assets/videos/ai-tests/arrow.mp4";
import dribblingMP4 from "@assets/videos/ai-tests/dribbling.mp4";
import highJumpMP4 from "@assets/videos/ai-tests/jump.mp4";
import ladderBFMP4 from "@assets/videos/ai-tests/ladder-b-f.mp4";
import ladderLRMP4 from "@assets/videos/ai-tests/ladder-l-r.mp4";
import powerKickMP4 from "@assets/videos/ai-tests/power-kick.mp4";
import pushUpsMP4 from "@assets/videos/ai-tests/push-ups.mp4";
import serpentMP4 from "@assets/videos/ai-tests/serpent.mp4";
//#endregion [Videos]

const testNames = {
  ru: [
    "Бег 15м со старта",
    "Лесенка лево-право",
    "Лесенка вперед-назад",
    "Прыжок вверх с места",
    "Змейка",
    "Пушечный удар правой/левой ногой",
    "Бег на максимальной скорости 15м",
    "Стрела",
    "Ведение 15м правой/левой с ход",
    "Стрела с мячом правой/левой ногой",
    "Отжимания",
  ],
  en: [
    "15m Sprint",
    "Coordination ladder left-right",
    "Coordination ladder back-forth",
    "High jump",
    "Serpent",
    "Power kick. RF/LF",
    "15m sprint from run up",
    "Arrow",
    "Dribbling 15m with run up. RF/LF",
    "Arrow with the ball. RF/LF",
    "Push ups",
  ],
};

const { lang = "en" } = Astro.props;

// Get localized test names for the current language
const localizedTestNames =
  testNames[lang as keyof typeof testNames] || testNames.en;
---

<div class="container is--tests swiper">
  <div class="tests is--preview swiper-wrapper">
    <!-- Tests -->
    <TestCard
      lang={lang}
      testName={localizedTestNames[0]}
      type="large"
      video={m15SprintMP4}
      categories={["pace", "agility"]}
      icon={Run15Icon}
      poster={optimizedPosters["Sprint15Poster"].src}
    />
    <!-- 3 cards -->
    <TestCard
      lang={lang}
      testName={localizedTestNames[1]}
      type="small"
      video={ladderLRMP4}
      categories={["pace", "agility"]}
      icon={LadderLRIcon}
      poster={optimizedPosters["LadderLRPoster"].src}
    />
    <TestCard
      lang={lang}
      testName={localizedTestNames[2]}
      type="small"
      video={ladderBFMP4}
      categories={["pace", "agility"]}
      icon={LadderFBIcon}
      poster={optimizedPosters["LadderBFPoster"].src}
    />
    <TestCard
      lang={lang}
      testName={localizedTestNames[3]}
      type="small"
      video={highJumpMP4}
      categories={["physical", "agility"]}
      icon={jumpIcon}
      poster={optimizedPosters["JumpPoster"].src}
    />
    <!-- 3 cards End-->

    <!-- 2 cards -->
    <TestCard
      lang={lang}
      testName={localizedTestNames[4]}
      type="medium"
      video={serpentMP4}
      categories={["dribbling"]}
      icon={SerpentIcon}
      poster={optimizedPosters["SerpentPoster"].src}
    />
    <TestCard
      lang={lang}
      testName={localizedTestNames[5]}
      type="medium"
      video={powerKickMP4}
      categories={["shooting"]}
      icon={KickIcon}
      poster={optimizedPosters["KickPoster"].src}
    />
    <!-- 2 cards End-->
    <TestCard
      lang={lang}
      testName={localizedTestNames[6]}
      type="large"
      video={m15SprintFromRunMP4}
      categories={["pace"]}
      icon={Run15upIcon}
      poster={optimizedPosters["Run15UpPoster"].src}
    />
    <TestCard
      lang={lang}
      testName={localizedTestNames[7]}
      type="large"
      video={arrowMP4}
      categories={["agility"]}
      icon={ArrowIcon}
      poster={optimizedPosters["ArrowPoster"].src}
    />
    <TestCard
      lang={lang}
      testName={localizedTestNames[8]}
      type="large"
      video={dribblingMP4}
      categories={["dribbling"]}
      icon={Dribbling15Icon}
      poster={optimizedPosters["DribblingPoster"].src}
    />
    <TestCard
      lang={lang}
      testName={localizedTestNames[9]}
      type="large"
      video={arrowWithBallMP4}
      categories={["dribbling"]}
      icon={ArrowBallIcon}
      poster={optimizedPosters["ArrowBallPoster"].src}
    />
    <TestCard
      lang={lang}
      testName={localizedTestNames[10]}
      type="large"
      video={pushUpsMP4}
      categories={["physical"]}
      icon={PushUpsIcon}
      poster={optimizedPosters["PushUpsPoster"].src}
    />

    <!-- Tests End -->
  </div>
  <div class="tests__pagination-wrap">
    <div class="tests__pagination">
      <button class="button tests__pagination-button is--prev">Prev</button>
      <p class="tests__pagination-counter p2">0 / 00</p>
      <button class="button tests__pagination-button is--next">Next</button>
    </div>
  </div>
  <div class="flex justify-center items-center tests__show-more-wrap">
    <button
      class="flex justify-center items-center gap-4 hover:bg-primary-500 p-4 rounded-2xl font-medium text-lg/tight lg:text-2xl transition-colors bg-accent-primary test__show-more-button"
    >
      Show all tests
    </button>
  </div>
</div>

<style>
  .container.is--tests {
    position: relative;
    overflow: visible;
  }

  .tests {
    display: grid;
    gap: 24px;
    grid-template-columns: repeat(6, 1fr);
    grid-auto-rows: auto;
  }

  @media screen and (max-width: 992px) {
    .tests {
      display: flex;
      gap: 0;
    }
  }

  /*#region [Pagination]*/
  .tests__pagination-wrap {
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    min-height: 48px;
    margin-top: 32px;
    display: none;
    position: sticky;
    z-index: 1000;
  }

  @media screen and (max-width: 992px) {
    .tests__pagination-wrap {
      display: flex;
    }
  }

  .tests__pagination {
    width: 100%;
    display: flex;
    gap: 24px;
    justify-content: center;
    align-items: center;
    background: rgba(217, 217, 217, 0.7);
    border: 1px solid #f1f1f1;
    border-radius: calc(1px * infinity);
    padding: 0.25rem;
  }

  .tests__pagination-button {
    padding: 8px;
    border-radius: calc(1px * infinity);
    background: rgba(255, 255, 255, 0.8);
    width: 100%;
  }

  @media screen and (min-width: 992px) {
    .tests__pagination-button:hover {
      background: rgba(255, 255, 255, 1);
    }
  }

  .tests__pagination-counter {
    white-space: nowrap;
    text-align: center;
  }
  /*#endregion [Pagination]*/
</style>

<style is:global>
  .tests__show-more-wrap {
    display: none;
  }

  @media screen and (min-width: 992px) {
    .tests.is--preview *:nth-child(n + 8 of .test) {
      display: none;
    }

    .tests.is--preview *:nth-child(7 of .test) {
      height: 200px;
      overflow: hidden;
    }

    .tests__show-more-wrap {
      position: absolute;
      top: auto;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      height: 200px;

      background: linear-gradient(
        180deg,
        rgba(244, 245, 249, 0.4) 0%,
        rgb(244 245 249) 80%
      );
      z-index: 100;
    }
  }
</style>

<script>
  import { Swiper } from "swiper";
  import { Navigation, Pagination } from "swiper/modules";

  // import Swiper and modules styles

  import { track } from "@amplitude/analytics-browser";
  import debounce from "@src/utils/debounce";

  //#region [Tracking]
  const regButtons = document.querySelectorAll(
    ".price__card-button"
  ) as NodeListOf<HTMLElement>;

  regButtons.forEach((button) => {
    button.addEventListener("click", () => {
      track("Click registration button", {
        academy: "Tests",
        button: button?.id,
      });
    });
  });

  //#endregion [Tracking]

  //#region [Show all tests]
  const showAllTriggerEl = document.querySelector(
    ".tests__show-more-wrap"
  ) as HTMLElement;
  const testGrid = document.querySelector(".tests") as HTMLElement;

  if (showAllTriggerEl && testGrid) {
    showAllTriggerEl.addEventListener("click", () => {
      testGrid.classList.remove("is--preview");
      showAllTriggerEl.style.display = "none";
    });
  }
  //#endregion [Show all tests]

  //#region [Tests slider]
  let testsSlider: Swiper | null = null;
  let previousWidth = window.innerWidth;

  function isMobileWidth(width: number) {
    return width <= 992;
  }

  function initTestsSlider() {
    console.log("InitSlider");

    if (testsSlider) {
      testsSlider.destroy();
      testsSlider = null;
    }

    testsSlider = new Swiper(".container.swiper", {
      modules: [Navigation, Pagination],
      spaceBetween: 16,
      autoHeight: true,
      slidesPerView: "auto",
      // slideToClickedSlide: true,
      // If we need pagination
      pagination: {
        el: ".tests__pagination-counter",
        type: "fraction",
      },

      // Navigation arrows
      navigation: {
        nextEl: ".tests__pagination-button.is--next",
        prevEl: ".tests__pagination-button.is--prev",
      },
    });
  }

  function destroyTestsSlider() {
    if (testsSlider) {
      console.log("Destroy slider");
      testsSlider.destroy();
      testsSlider = null;
    }
  }

  function handleSliderResize() {
    const currentWidth = window.innerWidth;
    const wasMobile = isMobileWidth(previousWidth);
    const isMobile = isMobileWidth(currentWidth);

    // Only act if we're crossing the mobile/desktop boundary
    if (wasMobile !== isMobile) {
      if (isMobile && !testsSlider) {
        initTestsSlider();
      } else if (!isMobile && testsSlider) {
        destroyTestsSlider();
      }
    }

    previousWidth = currentWidth;
  }

  // Initialize based on current viewport
  if (isMobileWidth(window.innerWidth)) {
    initTestsSlider();
  }

  // Add debounced resize listener
  const debouncedResize = debounce(handleSliderResize, 300);
  window.addEventListener("resize", debouncedResize);
  //#endregion [Tests slider]
</script>
