@use "@styles/variables" as *;
@use "@styles/screens" as *;

#faq {
  margin-top: $space-48;
}

.faq {
  margin: 0 auto;
  max-width: 47.5rem;
  display: flex;
  flex-direction: column;
  row-gap: $space-16;
}

.faq__item .faq__item + * {
  margin-top: $space-8;
}

.faq__gallery {
  margin-top: $space-16;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $space-16;

  @media screen and (max-width: $mobile) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.faq__gallery.is--1col {
  grid-template-columns: 1fr;
}

.faq__gallery a {
  cursor: zoom-in !important;
  @include transition-opacity;
}

.faq__gallery video {
  width: 100%;
}

.faq__gallery a:hover {
  opacity: 0.5;
}

/*#region [Faq item]*/
.faq__item .faq__item .faq__trigger-title {
  font-size: 1.4rem;
}

.faq__item {
  border-radius: $space-16;
  border: 1px solid $gray;
  overflow: hidden;
  background-color: $gray-light;
  @include transition-colors;

  &:hover {
    background-color: #eaecf2;
  }

  &.is--opened {
    background-color: $white;
  }

  &.is--opened > .faq__body {
    grid-template-rows: 1fr;
  }
}

.faq__trigger {
  padding: $space-16;
  display: flex;
  justify-content: space-between;
  align-items: center;
  column-gap: $space-32;

  cursor: pointer;
}

.faq__trigger-arrow {
  width: 24px;
  flex-shrink: 0;
  @include transition-transform;

  &.is--opened {
    transform: rotateZ(180deg);
  }
}

.faq__body {
  overflow: hidden;
  display: grid;
  grid-template-rows: 0fr;
  transition: grid-template-rows 0.3s ease-in-out;

  & a {
    text-decoration: underline;
    color: $blue;
    text-decoration-thickness: 1px;
    text-underline-offset: 4px;

    &:hover {
      text-decoration: none;
    }
  }
}

.faq__body-content-wrap {
  overflow: hidden;
}

.faq__body-content {
  padding: 0 $space-16 $space-16 $space-16;
}

.faq__body-content .faq__item:nth-child(2) {
  margin-top: $space-24;
}

.faq__feedback {
  display: flex;
  column-gap: $space-24;
  align-items: center;
  margin-top: $space-16;
}

.faq__feedback-text {
  font-size: 1rem;
}

.faq__feedback-buttons {
  display: flex;
  column-gap: $space-16;
}

.faq__feedback-button {
  width: 2rem;
  cursor: pointer;
  color: #6c6c6c;
  @include transition-colors;

  &:hover {
    color: $blue;
  }

  &.is--clicked {
    pointer-events: none;
    color: $blue;
  }
}

.faq__body-content .faq__body-content .faq__feedback {
  display: none;
}

.faq__body-content {
  & img {
    max-height: 500px;
    display: inline;
    height: 100%;
    object-fit: cover;

    @media screen and (max-width: $mobile) {
      object-position: top;
      height: 200px;
    }
  }
}
/*#endregion [Faq item]*/
