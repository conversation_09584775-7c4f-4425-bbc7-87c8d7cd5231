{
  "extends": "astro/tsconfigs/strict",
  "compilerOptions": {
    "types": ["unplugin-icons/types/astro"],
    "plugins": [
      {
        "name": "@astrojs/ts-plugin"
      }
    ],
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "node",
    "isolatedModules": true,

    // "allowJs": true,
    // "checkJs": true,
    "baseUrl": ".",
    "paths": {
      "@components/*": ["src/components/*"],
      "@assets/*": ["src/assets/*"],
      "@styles/*": ["src/styles/*"],
      "@layouts/*": ["src/layouts/*"],
      "@lang/*": ["src/lang/*"],
      "@src/*": ["src/*"]
    }
  }
}
