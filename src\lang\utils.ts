import { languages, ui, defaultLang, showDefaultLang } from "./langs";

// Use example: const lang = getLangFromUrl(Astro.url);
export function getLangFromUrl(url: URL | Location): string {
  const [, lang] = url.pathname.split("/") as Array<string>;
  if (lang in ui) return lang as string;
  return defaultLang;
}

// Access object by dots like "hero.banner.title"
export function getNestedFieldByStringKey(obj: any, path: string) {
  const squareBracketsRgx = /\[(\w|'|")*\]/g;
  const squareBracketsToDot = (sb: string) =>
    `.${sb.replace(/\[|\]|'|"/g, "")}`;
  const parts = path.replace(squareBracketsRgx, squareBracketsToDot).split(".");

  return parts.reduce((o: string, part: any) => o?.[part], obj);
}

function interpolateString(string: string, values: { [key: string]: string }) {
  const regex = /{(\w+)}/g;

  return string.replace(regex, (match, key) => {
    return values[key] || match;
  });
}

/**
 *
 * Get translated string from object. Use example:
 * const t = useTranslations(lang);
 * <p>{t("about.text")}</p>
 * @param lang
 * @returns
 */
export function useTranslations(lang: keyof typeof ui) {
  return function t(key: string, values?: { [key: string]: string }) {
    const string =
      getNestedFieldByStringKey(ui[lang], key) ??
      getNestedFieldByStringKey(ui[defaultLang], key);
    if (values) {
      return interpolateString(string, values);
    } else {
      return string;
    }
  };
}

// Automatically add lang prefix path to link
// Use example: <a href={translatePath("/")}>Home</a>
export function useTranslatedPath(lang: keyof typeof ui) {
  return function translatePath(path: string, l: keyof typeof ui = lang) {
    // Trailing slash at the end for SSG and 'directory' build format
    return !showDefaultLang && l === defaultLang
      ? (path + "/").replace("//", "/")
      : `/${String(l)}${path}/`.replace("//", "/");
  };
}

// Force change language
export function changeLang(
  selectedLang: string,
  returnString = false,
  location: Location | URL = window.location,
) {
  const { pathname, search, hash } = location;
  const [q, lang, ...rest] = pathname.split("/");
  const newLang = selectedLang === defaultLang ? "/" : `/${selectedLang}/`;

  let updatedPath = newLang;

  if (lang in languages) {
    updatedPath += rest.join("/");
  } else {
    updatedPath += [lang, ...rest].join("/");
  }

  if (returnString) {
    return encodeURI(updatedPath + search + hash).replace("//", "/");
  } else {
    window.location.href = encodeURI(updatedPath + search + hash).replace(
      "//",
      "/",
    );
  }
}
