---
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
} from "../lang/utils";

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

//#region [Active link]
const currentPath = new URL(Astro.request.url).pathname;
//#endregion [Active link]

interface Props {
  /** Will be automatically converted to corresponding language if {external} is false */
  link: string;
  class?: string;
  external?: boolean;
}

const { link, class: calssList, external } = Astro.props;
---

<a
  class:list={[
    "navbar__link",
    calssList,
    currentPath === translatePath(link) ? "active" : "",
  ]}
  href={external ? link : translatePath(link)}><slot /></a
>

<style>
  a.active {
    font-weight: 700;
  }
</style>
