---

---

<div class="support-bubble" data-opened="false">
  <div class="circle">
    <!-- prettier-ignore -->
    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="30" fill="none" viewBox="0 0 28 30"><path fill="#000" d="M15.313 15.3c0 .269-.078.531-.222.755a1.312 1.312 0 0 1-.589.5c-.24.104-.504.13-.758.078a1.299 1.299 0 0 1-.672-.372 1.376 1.376 0 0 1-.36-.696 1.406 1.406 0 0 1 .075-.785c.1-.249.268-.461.484-.61a1.28 1.28 0 0 1 1.657.169c.246.255.384.6.384.96Zm-6.126-1.36c-.26 0-.513.08-.729.23a1.35 1.35 0 0 0-.483.61c-.1.248-.125.521-.075.785s.176.506.36.696c.183.19.417.32.671.372.255.053.519.026.759-.077s.445-.277.589-.5a1.395 1.395 0 0 0-.163-1.717 1.29 1.29 0 0 0-.928-.399Zm9.626 0c-.26 0-.514.08-.73.23a1.35 1.35 0 0 0-.483.61c-.1.248-.125.521-.075.785s.176.506.36.696a1.271 1.271 0 0 0 1.43.295c.24-.103.445-.277.589-.5a1.395 1.395 0 0 0-.163-1.717 1.29 1.29 0 0 0-.928-.399Zm6.562 1.36a12.11 12.11 0 0 1-1.475 5.804 11.602 11.602 0 0 1-4.043 4.298 11.088 11.088 0 0 1-5.564 1.678 11.058 11.058 0 0 1-5.64-1.379L4.93 26.987c-.309.107-.64.122-.956.045a1.735 1.735 0 0 1-.836-.483 1.83 1.83 0 0 1-.465-.865 1.87 1.87 0 0 1 .043-.99l1.241-3.857a12.11 12.11 0 0 1-1.325-5.12 12.16 12.16 0 0 1 .975-5.202 11.753 11.753 0 0 1 3.08-4.233 11.237 11.237 0 0 1 4.558-2.408 10.993 10.993 0 0 1 5.117-.094A11.204 11.204 0 0 1 21 6.018a11.71 11.71 0 0 1 3.222 4.118 12.13 12.13 0 0 1 1.153 5.164Zm-1.75 0c0-1.53-.34-3.038-.994-4.41a9.902 9.902 0 0 0-2.774-3.498 9.471 9.471 0 0 0-3.983-1.867 9.303 9.303 0 0 0-4.369.15A9.524 9.524 0 0 0 7.651 7.81a9.968 9.968 0 0 0-2.545 3.682 10.289 10.289 0 0 0 .56 8.799.93.93 0 0 1 .073.74L4.375 25.27l4.09-1.413a.83.83 0 0 1 .715.075 9.371 9.371 0 0 0 4.814 1.34 9.37 9.37 0 0 0 4.816-1.334 9.804 9.804 0 0 0 3.525-3.65 10.244 10.244 0 0 0 1.29-4.987Z"/></svg>
  </div>
  <ul class="options">
    <li class="option-item" data-channel="Telegram">
      <a href="https://t.me/junistat_support_bot" target="_blank">
        <!--prettier-ignore -->
        <svg class="option-icon" xmlns="http://www.w3.org/2000/svg" width="53" height="53" fill="none" viewBox="0 0 53 53"><path fill="#2CA4E0" d="M26.383 52.765c14.57 0 26.382-11.812 26.382-26.383C52.765 11.813 40.953 0 26.382 0 11.813 0 0 11.812 0 26.383c0 14.57 11.812 26.382 26.383 26.382Z"/><path fill="#fff" fill-rule="evenodd" d="M11.942 26.104c7.691-3.35 12.82-5.56 15.386-6.627 7.327-3.048 8.85-3.577 9.841-3.595.22-.003.707.05 1.023.307.267.217.34.51.375.715.035.205.08.673.044 1.038-.397 4.172-2.115 14.295-2.989 18.968-.37 1.977-1.098 2.64-1.803 2.705-1.532.14-2.695-1.013-4.18-1.986-2.32-1.522-3.633-2.47-5.887-3.954-2.605-1.717-.916-2.66.568-4.202.39-.404 7.14-6.544 7.27-7.1.016-.07.032-.33-.123-.467-.154-.137-.382-.09-.546-.053-.233.053-3.942 2.505-11.128 7.355-1.053.723-2.007 1.076-2.86 1.057-.943-.02-2.755-.532-4.102-.97-1.653-.537-2.966-.821-2.85-1.733.06-.476.712-.961 1.961-1.458Z" clip-rule="evenodd"/></svg>
        <p>Telegram</p>
      </a>
    </li>
    <li class="option-item" data-channel="WhatsApp">
      <a href="https://wa.me/+77772378325" target="_blank">
        <!--prettier-ignore -->
        <svg class="option-icon" xmlns="http://www.w3.org/2000/svg" width="53" height="53" fill="none" viewBox="0 0 53 53"><path fill="#42E760" d="M0 25.327C0 13.387 0 7.418 3.71 3.71 7.417 0 13.387 0 25.326 0h2.11c11.94 0 17.91 0 21.619 3.71 3.709 3.708 3.709 9.678 3.709 21.617v2.11c0 11.94 0 17.91-3.71 21.619-3.708 3.709-9.678 3.709-21.617 3.709h-2.11c-11.94 0-17.91 0-21.619-3.71C0 45.348 0 39.378 0 27.439v-2.112Z"/><path fill="#31D652" d="m19.418 36.916.483.24c2.013 1.193 4.268 1.75 6.522 1.75 7.085 0 12.882-5.73 12.882-12.733 0-3.343-1.368-6.605-3.784-8.993a12.865 12.865 0 0 0-9.098-3.74c-7.085 0-12.882 5.73-12.802 12.812 0 2.388.725 4.696 1.932 6.685l.322.477-1.288 4.695 4.83-1.194Z"/><path fill="#fff" d="M36.89 15.906c-2.738-2.786-6.522-4.298-10.387-4.298-8.212 0-14.814 6.606-14.734 14.643 0 2.547.725 5.014 1.933 7.242l-2.094 7.56 7.81-1.989c2.174 1.194 4.59 1.75 7.005 1.75 8.132 0 14.734-6.605 14.734-14.642 0-3.9-1.53-7.56-4.267-10.266ZM26.503 38.348c-2.173 0-4.347-.557-6.2-1.67l-.482-.24-4.67 1.194 1.208-4.536-.322-.478c-3.543-5.65-1.852-13.13 3.945-16.633 5.797-3.5 13.285-1.83 16.827 3.9 3.543 5.73 1.852 13.13-3.945 16.632-1.852 1.194-4.106 1.83-6.36 1.83l-.001.001Zm7.086-8.834-.886-.398s-1.288-.557-2.093-.955c-.08 0-.161-.08-.242-.08-.241 0-.402.08-.563.16 0 0-.08.08-1.208 1.353a.444.444 0 0 1-.403.24h-.08c-.08 0-.242-.08-.322-.16l-.403-.16c-.886-.398-1.69-.875-2.335-1.512-.16-.159-.402-.318-.563-.477-.564-.557-1.128-1.194-1.53-1.91l-.08-.16c-.081-.079-.081-.158-.162-.318 0-.159 0-.318.08-.398 0 0 .323-.398.564-.636.162-.16.242-.398.403-.557.16-.24.242-.557.16-.796-.08-.398-1.046-2.547-1.287-3.024-.161-.239-.322-.319-.564-.398h-.885c-.162 0-.322.08-.484.08l-.08.08c-.161.08-.322.238-.483.317-.161.16-.242.32-.403.478-.563.716-.885 1.591-.885 2.467 0 .637.16 1.273.402 1.83l.08.24a13.266 13.266 0 0 0 2.98 4.058l.322.318c.24.24.483.398.644.637 1.69 1.432 3.623 2.467 5.797 3.024.242.08.564.08.805.16h.805c.403 0 .886-.16 1.208-.319.24-.16.402-.16.564-.319l.16-.159c.162-.16.323-.239.484-.398.16-.159.322-.318.402-.477.16-.319.242-.717.322-1.114v-.558s-.08-.08-.241-.159Z"/></svg>
        <p>WhatsApp</p>
      </a>
    </li>
  </ul>
</div>

<style lang="scss">
  @use 'sass:color';
  @use "@styles/_variables.scss" as *;
  @use "@styles/_screens.scss" as *;

  .support-bubble {
    display: flex;
    align-items: flex-end;
    position: fixed;
    z-index: 10000;
    bottom: 5%;
    right: 24px;
  }

  .support-bubble[data-opened="true"] {
    ul.options {
      opacity: 1;
    }
  }

  .support-bubble[data-opened="false"] {
    ul.options {
      opacity: 0;
      visibility: hidden;
    }
  }

  .circle {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 64px;
    height: 64px;
    padding: 16px;
    border-radius: 50%;
    background-color: $primary;

    position: relative;
    z-index: 100;

    @include transition-colors;

    @media screen and (min-width: $tablet) {
      &:hover {
        background-color: color.change($primary, $lightness: 45%);
      }
    }
  }

  ul.options {
    background-color: $white;
    border-radius: 8px;
    padding: 0;
    border: 1px solid $gray;

    position: absolute;
    z-index: 50;
    bottom: 120%;
    right: 0%;

    @include transition-opacity;
  }

  .option-item {
    @media screen and (min-width: $tablet) {
      @include transition-colors;

      &:hover {
        background-color: $gray-light;
      }
    }
  }

  .option-item > a {
    display: flex;
    align-items: center;
    padding: 8px;
  }

  .option-icon {
    width: 28px;
    height: 28px;
    margin-right: $space-8;
  }
</style>

<script>
  import { track } from "@amplitude/analytics-browser";

  const bubbles = document.querySelectorAll(
    ".support-bubble",
  ) as NodeListOf<HTMLElement>;
  bubbles.forEach((bubble) => {
    bubble.addEventListener("click", () => {
      if (bubble.dataset.opened === "false") {
        bubble.dataset.opened = "true";
      } else {
        bubble.dataset.opened = "false";
      }
    });

    window.addEventListener("click", (e) => {
      const target = e.target as Element;
      if (e.target && !target.closest(".support-bubble")) {
        bubble.dataset.opened = "false";
      }
    });
  });

  const chatOptions = document.querySelectorAll(
    ".option-item",
  ) as NodeListOf<HTMLElement>;
  chatOptions.forEach((option) => {
    option.addEventListener("click", () => {
      track("Click chat button", {
        channel: option.dataset.channel,
      });
    });
  });
</script>
