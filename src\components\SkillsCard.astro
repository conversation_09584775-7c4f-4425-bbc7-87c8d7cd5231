---
type SkillsGroup = Array<{ [key: string]: [string, number] }>;
interface Props {
  skillCardName: string;
  skillCardPoints: number;
  skills: SkillsGroup;
}

const { skillCardName, skillCardPoints, skills } = Astro.props;

function setBarColor(n: number) {
  if (n >= 60) {
    return "is--green";
  } else if (n < 60 && n >= 30) {
    return "is--orange";
  } else {
    return "is--red";
  }
}
---

<div class="skills-card">
  <div class="skills-card__header">
    <div class="skill-card__top">
      <div class="skill-card__card-name">{skillCardName}</div>
      <div class="skill-card__card-digits">{skillCardPoints}</div>
    </div>
    <div
      style={`width:${skillCardPoints}%`}
      class:list={["skill-card__bar", setBarColor(skillCardPoints)]}
    >
    </div>
  </div>

  <div class="skills-card__list">
    {
      skills.map((skill: { [key: string]: [string, number] }, i: number) => {
        const [skillName, value]: [string, [string, number]] =
          Object.entries(skill)[0];
        return (
          <div class="skills-card__item">
            <div class="skills-card__skill-data">
              <p class="skills-card__name">{skillName}</p>
              <p class="skills-card__digits">{value[0]}</p>
            </div>
            <div class="skill-card__bar">
              <div
                class:list={["skill-card__bar-line", setBarColor(value[1])]}
                style={`width:${value[1]}%`}
              />
            </div>
          </div>
        );
      })
    }
  </div>
</div>

<style lang="scss">
  @use "@styles/_variables.scss" as v;
  @use "@styles/_screens.scss" as screen;

  .skills-card {
    background-color: v.$white;
    overflow: hidden;
    border-radius: v.$space-8;
    box-shadow:
      0px 1px 2px rgba(0, 0, 0, 0.06),
      0px 1px 4px rgba(0, 0, 0, 0.1);
  }
  .skills-card__header {
    & .skill-card__bar {
      width: 100%;
      height: 1rem;
    }
  }

  .skill-card__top {
    display: flex;
    padding: v.$space-24 v.$space-24 v.$space-8;
    justify-content: space-between;
    text-transform: uppercase;
    font-size: 1.31rem;
    font-weight: 700;
  }

  .skills-card__list {
    display: flex;
    flex-direction: column;
    gap: v.$space-16;
    padding: v.$space-16 v.$space-24 v.$space-24 v.$space-24;
    width: 100%;
  }

  .skills-card__item {
    width: 100%;

    .skill-card__bar {
      margin-top: 4px;
      width: 100%;
      height: 0.25rem;
      background-color: #dfe5ea;
      border-radius: 100px;
    }

    & p {
      font-size: 1rem;
      font-weight: 500;
    }

    & .skill-card__bar-line {
      width: 50%;
      height: 100%;
    }
  }

  .is--green {
    background-color: #00ad1c;
  }
  .is--orange {
    background-color: #ff903f;
  }
  .is--red {
    background-color: #da3c5d;
  }

  .skills-card__skill-data {
    display: flex;
    justify-content: space-between;
  }
</style>
