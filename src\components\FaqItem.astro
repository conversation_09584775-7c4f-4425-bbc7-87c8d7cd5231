---
const { question } = Astro.props;
---

<div class="faq__item">
  <div class="faq__trigger">
    <h4 class="faq__trigger-title">{question.title}</h4>
    <svg
      class="faq__trigger-arrow"
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
    >
      <path
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="1.5"
        d="m19 8.5-7 7-7-7"
      >
      </path>
    </svg>
  </div>
  <div class="faq__body">
    <div class="faq__body-content-wrap">
      <div class="faq__body-content">
        <p set:html={question.answer} />
        {
          question["sub_answers"] &&
            question["sub_answers"].map((sub: any) => {
              return <Astro.self question={sub} />;
            })
        }
      </div>
    </div>
  </div>
</div>

<script>
  import { track } from "@amplitude/analytics-browser";

  // Close/Open
  const faqItems = document.querySelectorAll(
    ".faq__item"
  ) as NodeListOf<HTMLElement>;

  faqItems.forEach((item) => {
    const trigger = item.querySelector(".faq__trigger");
    const arrow = item.querySelector(".faq__trigger-arrow");

    if (!trigger || !arrow) return;
    trigger.addEventListener("click", () => {
      // Track open question
      if (!item.classList.contains("is--opened")) {
        track("Open faq item", {
          question: trigger.querySelector(".faq__trigger-title")?.textContent,
        });
      }

      item.classList.toggle("is--opened");
      arrow.classList.toggle("is--opened");
    });
  });

  // Wrap images with link
  const images = document.querySelectorAll(
    ".faq__gallery img"
  ) as NodeListOf<HTMLImageElement>;

  images.forEach((img) => {
    const newLink = document.createElement("a");
    newLink.setAttribute("href", img.src);
    newLink.setAttribute("target", "_blank");

    if (img.parentNode) {
      img.parentNode.insertBefore(newLink, img);
      newLink.appendChild(img);
    }
  });
</script>
