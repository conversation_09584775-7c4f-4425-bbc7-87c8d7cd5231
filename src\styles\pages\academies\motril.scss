@use "@styles/variables" as *;
@use "@styles/screens" as *;

/*#region [Font]*/

h1,
.h1 {
  font-family: "Roboto-Flex", sans-serif;
  font-style: italic;

  @media screen and (min-width: $tablet) {
    font-size: 40px;
  }

  @media screen and (max-width: $mobile) {
    font-size: 24px;
  }
}

h2,
.h2 {
  font-family: "Roboto-Flex", sans-serif;
  font-style: italic;
  @media screen and (min-width: $tablet) {
    font-size: 32px;
    font-weight: 600;
  }

  @media screen and (max-width: $mobile) {
    font-size: 22px;
  }
}

h3,
.h3 {
  font-family: "Roboto-Flex", sans-serif;
  font-style: italic;
  @media screen and (min-width: $tablet) {
    font-size: 24px;
  }

  @media screen and (max-width: $mobile) {
    font-size: 18px;
  }
}

p,
.p {
  font-family: "Roboto-Flex", sans-serif;
  font-size: 16px;
}

strong {
  font-weight: 800;
}
/*#endregion [Font]*/

/*#region [Common]*/
.card {
  display: flex;
  padding: $space-24;
  border-radius: $space-16;
  background-color: $white;
  border: 1px solid #d9d9d9;

  gap: $space-24;

  @media screen and (max-width: $mobile) {
    flex-direction: column;

    gap: $space-16;
  }
}

ul > * + * {
  margin-top: 4px;
}

.guide__profile-examples h3 + * {
  margin-top: $space-8;
}
/*#endregion [Common]*/

/*#region [Hero]*/
.section.is--hero {
  overflow: visible;
}

.hero {
  margin-top: $space-88;
  margin-left: auto;
  margin-right: auto;
  max-width: 840px;
  display: flex;
  row-gap: $space-24;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.hero__logo {
  width: auto;
  max-width: 350px;
  max-height: 200px;
}

.hero__logo.is--circle {
  border-radius: 1000px;
}

/*#endregion [Hero]*/

/*#region [Main]*/
.main {
  display: grid;
  column-gap: $space-64;
  grid-template-columns: 0.8fr 1fr;
  margin-bottom: $space-64;

  @media screen and (max-width: $tablet) {
    grid-template-columns: 1fr;
  }
}
/*#endregion [Main]*/

/*#region [Guide]*/
.main__col.guide > * + * {
  margin-top: $space-16;
}

.guide__circle-number {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background-color: $primary;
  border-radius: 50%;
  font-weight: 800;
}

.guide__card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  row-gap: $space-16;
}
.motril-header {
  display: flex;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: end;
  padding: $space-16;
}

.guide__card-headline {
  font-weight: 700;
}
.guide__card-price-block {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.guide__card-price {
  background: #ffe83f;
  padding: 4px 8px;
  font-weight: 700;
  margin-right: 8px;
}
.motril-select-text {
  background: #ffe83f;
}

.guide__card-price-old {
  font-style: italic;
  text-decoration: line-through;
  font-weight: 700;
  color: #979797;
}

ul.guide__card-list {
  list-style: unset;
  padding-left: $space-24;
}

.guide__profile-cards {
  margin-top: $space-16;
  display: flex;
  flex-direction: row;
  gap: $space-16;

  > img {
    max-width: fit-content;
  }

  //   @media screen and (max-width: $mobile) {
  //     > a:last-child {
  //       display: none;
  //     }
  //   }
}

@media screen and (min-width: $tablet) {
  .guide__card-link {
    @include transition-all;

    &:hover {
      transform: scale(1.02);
      opacity: 0.8;
    }
  }
}

/*#endregion [Guide]*/

/*#region [Info]*/

@media screen and (max-width: $mobile) {
  .info {
    margin-top: $space-16;
  }
}

.main__col.info > * + * {
  margin-top: $space-16;
}

.card.info {
  flex-direction: column;
  row-gap: $space-32;
}

/*#region [Video]*/

.info__video {
  border-radius: 1rem;
  overflow: hidden;
}

.video {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 10;
  overflow: hidden;
  border-radius: 16px;
  isolation: isolate;
}

/*#endregion [Video]*/

.info__card-header > * + * {
  margin-top: $space-16;
}

.info__tests-group {
  margin-top: $space-16;
  margin-bottom: $space-16;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $space-8;

  @media screen and (max-width: $mobile) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.info__test {
  display: flex;
  align-items: center;
  padding: $space-8;
  padding-right: $space-16;
  background-color: $gray-light;
  border-radius: $space-8;
}

.info__test > img {
  margin-right: $space-16;
  width: 50px;
  height: 50px;
}

.info__tests-nowrap {
  white-space: nowrap !important;
}

.info__test-icon {
  border-radius: 4px;
}
.link_underline {
  text-decoration: underline;
}
/*#endregion [Info]*/
