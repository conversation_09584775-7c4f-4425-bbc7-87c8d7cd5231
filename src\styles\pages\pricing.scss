@use "@styles/variables" as *;
@use "@styles/screens" as *;

.section__bg-1 {
  background: linear-gradient(100.18deg, #ffe000 0%, #fff854 91.37%);
}
.section__bg-line-1 {
  background: linear-gradient(97.83deg, #ffa800 1.33%, #fffa7c 75.8%);

  z-index: 10;
}
.section__bg-line-2 {
  background: linear-gradient(97.83deg, #ffa800 1.33%, #fffa7c 75.8%);

  opacity: 0.65;
}
.section__bg-line-3 {
  background: linear-gradient(98.85deg, #fff72d 0%, #ffd600 93.26%);
}
.section__bg-line-4 {
  background: linear-gradient(98.85deg, #fff72d 0%, #ffd600 93.26%);
}

.section.is--hero.is--pricing {
  padding-top: 120px;
  position: relative;
  z-index: 10;
}

.section__pricing-bg {
  z-index: 1;
  position: absolute;
  bottom: auto;
  left: 0%;
  right: 0%;
  transform: skew(0deg, -12deg);
}

.pricing__headline {
  text-align: center;
  margin-bottom: 64px;
}

.pricing {
  flex-direction: column;
  align-items: center;
  display: flex;
}

.pricing__grid {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-content: center;
  align-items: center;
  margin-bottom: 94px;
  padding-left: 146px;
  padding-right: 146px;
  display: grid;

  @media screen and (max-width: $mobile) {
    grid-template-columns: 1fr;
    padding-left: 0;
    padding-right: 0;
  }
}

.pricing__plans {
  background-color: #fff;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding: 32px;
}

.pricing__license {
  color: #fff;
  background-color: #2d2c10;
  border-radius: 8px;
  padding: 32px;
}

.pricing__plans-headline {
  margin-bottom: 8px;
}

.pricing__plans-subtitle {
  margin-bottom: 32px;
  font-size: 24px;
  line-height: 100%;
}

.pricing__plans-cards-wrap {
  grid-row-gap: 32px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.pricing__plans-card-title {
  width: 100%;
  border-top: 3px solid #f2f2f2;
  border-left: 3px solid #f2f2f2;
  border-right: 3px solid #f2f2f2;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding: 16px;
  font-weight: 500;
  display: inline-block;
}

.pricing__plans-price-wrap {
  background-color: #ffe83f;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  display: flex;
}

.pricing__plans-price {
  flex: 1;
  font-size: 18px;
}

.pricing__plans-price-divider {
  width: 1px;
  height: 24px;
  background-color: #c8b72d;
  flex: none;
  margin-left: 16px;
  margin-right: 16px;
}

.pricing__license-headline {
  margin-bottom: 32px;
}

.pricing__license-features-list {
  grid-row-gap: 32px;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  list-style-type: none;
  display: grid;
}

.pricing__license-features-item {
  align-items: flex-start;
  font-size: 21px;
  display: flex;
}

.pricing__license-features-bullet {
  width: 18px;
  height: 4px;
  background-color: #ffe83f;
  flex: none;
  position: relative;
  top: 0.5em;
  transform: rotate(-20deg);
}

.pricing__license-features-text {
  flex: 1;
  margin-left: 16px;
}

.section__bg-1 {
  z-index: 0;
  height: 970px;
  position: relative;
  top: -373px;
  bottom: auto;
  left: 0%;
  right: 0%;
}

.section__bg-line-1 {
  width: 33%;
  height: 14%;
  position: absolute;
  top: auto;
  bottom: 42%;
  left: 0%;
  right: 0%;
}

.section__bg-line-2 {
  width: 23%;
  height: 14%;
  position: absolute;
  top: auto;
  bottom: 28%;
  left: 5%;
  right: 0%;
}

.section__bg-line-3 {
  width: 33%;
  height: 14%;
  position: absolute;
  bottom: 14%;
  left: 0%;
  right: 0%;
}

.section__bg-line-4 {
  width: 33%;
  height: 14%;
  position: absolute;
  bottom: 10%;
  right: 0%;
}

.pricing__recommentation-text {
  max-width: 624px;
  text-align: center;
  font-size: 21px;
}

.pricing__link {
  color: #007ae4;
  text-decoration: underline;
}

.pricing__link:hover {
  text-decoration: none;
}

.pricing__headline {
  margin-bottom: 48px;
}

.pricing__plans,
.pricing__license {
  padding-left: 16px;
  padding-right: 16px;
}

.pricing__plans-headline {
  font-size: 32px;
}

.pricing__plans-cards-wrap {
  grid-row-gap: 16px;
}

.pricing__plans-card-title {
  font-size: 18px;
}

.pricing__plans-price {
  font-size: 16px;
}

.pricing__license-headline {
  font-size: 32px;
}

.pricing__license-features-item {
  font-size: 16px;
}

.pricing__recommentation-text {
  font-size: 18px;
}

#w-node-_8bb55600-962c-72e1-b722-954770b9a22c-9de886d9,
#w-node-_8bb55600-962c-72e1-b722-954770b9a24d-9de886d9,
#w-node-_5ce9f970-f923-a899-33c2-f08d3c69ca59-d3e886da,
#w-node-_6691f01e-24a4-a8e5-aef9-bf94dee9a400-d3e886da,
#w-node-_5ce9f970-f923-a899-33c2-f08d3c69ca59-bae886db,
#w-node-_6691f01e-24a4-a8e5-aef9-bf94dee9a400-bae886db,
#w-node-_9aacbea9-2cd5-6e48-9664-45453b1f9293-5c594910,
#w-node-_9aacbea9-2cd5-6e48-9664-45453b1f9294-5c594910,
#w-node-_9aacbea9-2cd5-6e48-9664-45453b1f9295-5c594910,
#w-node-_9aacbea9-2cd5-6e48-9664-45453b1f9297-5c594910 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

@media screen and (max-width: 991px) {
  #w-node-_288eadc5-6f23-7646-bfdb-3d5503b48bb2-44e886bf {
    grid-area: 2 / 1 / 3 / 2;
  }
}
