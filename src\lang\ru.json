{"about": {"investors_cite": "Цифровизация неизбежна для многих отраслей, и спорт не является исключением", "investors_cite_title": "инвестиционный директор Brayne", "investors_title": "Инвесторы", "media_title": "Медиа", "partners_title": "Пар<PERSON><PERSON>ё<PERSON>ы", "subtitle": "", "subtitle_2": "", "text": "Мы разрабатываем передовые мобильные технологии в области искусственного интеллекта и машинного зрения и взаимодействуем с футбольным сообществом, чтобы создать цифровой хаб для юных футболистов, тренеров, скаутов и клубов по всему миру. Используя только камеру телефона, наши приложения — JuniStat & JuniCoach — помогают определять физическую и техническую подготовку игроков, следить за их прогрессом и получать рекомендации по развитию.{br}{br}На основе этой спортивной технологии мы создаем TID (talent identification system/систему определения талантов), в которой у юных игроков есть цифровые профили и рейтинги в соответствии с их достижениями. Эта система собирает и предоставляет объективные статистические данные о юных футболистах по всему миру и значительно увеличивает шансы игроков попасть в поле зрения клубов и скаутов без каких-либо барьеров, а только за счет их таланта и мотивации.", "title": "JuniStat{copyright} — компания, основанная в 2020 году командой предпринимателей и ИТ-инженеров"}, "clubs": {"app_coach": "Приложение для тренера", "connect_club": "Подключить клуб", "create_club": "Регистрация клуба", "exercises": {"title": "14 цифровых тестов доступны в приложении"}, "exercises_subtitle": "Тесты интегрированы в мобильное приложение для наиболее удобного использования тренерами, скаутами и игроками. Каждый тест достоверно показывает метрики футболиста и средние данные по его возрастной группе.", "exercises_title": "Более 15 тестов", "features": {"card": {"description": "Профиль с логотипом клуба", "title": "Цифровая карточка игрока"}, "data": {"card_title": "Об игроке", "description_text": "Футболист со страстью к игре и стремлением к успеху. Алекс — нападающий с молниеносной работой ног, отличным контролем мяча и внимательным взглядом на цель. У него природный талант читать игру и принимать решения в доли секунды, что часто приводит к голевым моментам для его команды.", "height": "Высота, см", "position": "Позиция", "position_value": "CM, LCM", "title": "Дополнительно", "weight": "Вес, кг"}, "results": {"title": "Результаты тестов"}, "title": "После тестирования каждый игрок получает", "video": {"title": "Видео-нарезки игрока"}}, "features_card_text1": "Каждый игрок получает цифровую карточку и брендированный профиль", "features_card_text2": "Все данные игроков защищены и хранятся в соответствии с GDPR, ISO и промышленными стандартами страны. Каждый игрок связан со своим взрослым представителем", "features_card_text3": "Клуб делает игроков закрытыми или открытыми для просмотра на свое усмотрение", "guide_title": "Пошаговая инструкция по работе с системой Juni", "hero": {"points": [{"they": "Лазеры и альтернативы", "we": "Легко использовать,<br />низкая стоимость"}, {"they": "Субъективные оценки", "we": "Объективная статистика AI/ML"}, {"they": "Традиционный анализ", "we": "65+ параметров физической <br />и технической подготовки"}], "request_presentation": "Запросить презентацию", "reg_button": "Создать аккаунт", "reg_description": "Попробуйте 15 тестов бесплатно", "subtitle": "Система тестирования помогает академиям и клубам собирать 65+ показателей для определения талантов и развития молодежи", "title": "Отслеживайте и оценивайте навыки игроков с помощью приложения JuniCoach", "form": {"name_input": "Имя Фамилия", "email_input": "Email", "phone_input": "Телефон", "academy_input": "Название академии", "send_button": "Запросить презентацию", "legal_text": "Нажимая на кнопку вы соглашаетесь с ", "legal_text_privacy": "политикой конфиденциальности", "form_error": "Извините! Похоже, ваш запрос не был отправлен. Пожалуйста, попробуйте отправить его позже. Если проблема сохраняется, сообщите нам на <EMAIL>.", "form_success": "Спасибо. Мы свяжемся с вами в ближайшее время"}}, "how": {"equip": "Минимум оборудования для точного сбора данных", "equip_item": {"cones": "Фишки/Конусы", "phone": "Смартфон", "tape": "Рулетка", "tripod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "free_trial": "Бесплатный пробный доступ", "point1": {"title": "Тренер снимает видео по инструкции в приложении"}, "point2": {"title": "Платформа мгновенно обрабатывает данные и выдает точные показатели игроков"}, "title": "Как это работает"}, "player_example_title": "Пример карточки игрока", "player_open_profile": "Открыть профиль", "profile_protect_text": "Все данные игроков защищены и хранятся в соответствии с GDPR, ISO и промышленными стандартами страны. Каждый игрок связан со своим взрослым представителем", "quote": "«Систематические тестирования существенно повышают уровень обучаемости юных игроков и способствуют развитию основных футбольных навыков»", "recommends": {"ian_text": "“Эта технология измеряет технические и физические характеристики игроков, которые научно проверены на влияние на результаты футбола. Академия теперь может быстро тестировать игроков с помощью смартфона и штатива, вскоре загружать эти данные в цифровой центр и углубляться в уровни производительности отдельных игроков. <br /> Данные теперь управляют всем процессом идентификации талантов и развития, и Академия может быстро получить доступ к данным, чтобы внести любые необходимые изменения в индивидуальный план тренировок игрока. <br /> Оценивая возможности игроков в Европе, академии стремятся сопоставить потенциал с текущими показателями. Следовательно, важно отслеживать скорость, с которой молодые игроки развиваются с течением времени, и насколько последовательно они улучшаются.”", "show_less": "Свернуть", "show_more": "Показать всё"}, "recommends_title": "Нас рекомендуют", "rendered_subtitle": "Позволяет клубу быстро и эффективно находить требуемых игроков и проводить цифровой скаутинг", "rendered_title": "Система умного тестирования", "step_text1": "Зарегистрируйте клуб. После одобрения, вы получите доступ к системе", "step_text2": "Внесите в личный кабинет Клуба данные тренеров и игроков", "step_text3": "Свяжитесь с нами, чтобы назначить день пробного тестирования. Мы с удовольствием обучим ваших тренеров работе с системой", "steps": {"text1": "Мониторинг и сравнение данных игроков в сравнении с игроками во всём мире", "text2": "Анализируйте данные с помощью своих инструментов или используйте найденные инсайты", "text3": "Скаутинг и отбор игроков для участия в просмотре", "title": "Зарегистрируйтесь и исследуйте возможности платформы"}, "title": "JuniCoach{copyright} помогает быстро тестировать футболистов в связке с регулярными тренировками", "trial_testing": "Пробное тестирование"}, "whitelabel": {"page_title": "Whitelabel-решения для AI-тестирования футболистов", "hero": {"badge": "100+ клубов и академий", "title": "Whitelabel-решения для AI-тестирования футболистов", "description": "Собственный брендированный раздел с батареей тестов в приложении JuniCoach, с логотипом и цветами вашего клуба при заказе от 10 000 тестов"}, "features": {"access": "Доступ тренеров и скаутов к результатам в личном кабинете и мобильном приложении", "pricing": "Тесты по специальной цене при контрактах от 2 млн ₽", "custom_tests": "Возможность разработки тестов под требования клуба"}, "technology": {"title": "Передовая технология тестирования и скаутинга в цветах клуба", "description": "Используя только мобильные телефоны и штативы тренеры могут быстро тестировать и получать точные данные футболистов, сравнивать их с нормативами, определять и развивать таланты", "recommendation": "Проверена специалистами РФС и применяется для комплексной оценки игроков", "recommendation_link": "Рекомендательное письмо ↗"}, "equipment": {"title": "Тестирование без дорогого оборудования, в любом месте", "process1": "Тренер снимает видео по инструкции в приложении", "process2": "Платформа мгновенно обрабатывает данные и выдаёт точные показатели игроков"}, "tests": {"title": "Тестирование без дорогого оборудования, в любом месте"}, "modern": {"title": "Современный подход к отбору и развитию футболистов", "list1": "Сравнивайте игроков с ровесниками по всему миру", "list2": "Делитесь профилями с родителями", "list3": "Подтверждайте решения тренеров с помощью объективных данных", "security": "Безопасность и сохранность данных под строгим контролем"}, "media": {"title": "СМИ о нас", "article1": "Freedom QJ League провела цифровое тестирование игроков", "article2": "Программа цифровизации учеников «Зенит-Чемпионика»", "article3": "Отбор игроков на фестиваль с помощью умного тестирования"}, "cta": {"title": "Подключите свой бренд в JuniCoach", "contact_manager": "Связаться с менеджером", "description": "Ответим на вопросы и предложим варианты интеграции", "instructions": "Инструкция по работе с системой"}, "modal": {"title": "Связаться с менеджером", "close": "Закрыть модальное окно", "name_label": "Имя *", "name_placeholder": "Введите ваше имя", "email_label": "Email *", "email_placeholder": "<EMAIL>", "phone_label": "Телефон *", "phone_placeholder": "+7 (999) 123-45-67", "academy_label": "Название академии/клуба *", "academy_placeholder": "Название вашей академии или клуба", "submit": "Отправить заявку", "submitting": "Отправка...", "legal": "Нажимая кнопку, вы соглашаетесь с", "privacy_link": "политикой конфиденциальности", "success_title": "Заявка отправлена!", "success_message": "Мы свяжемся с вами в ближайшее время", "error_title": "Ошибка отправки", "error_message": "Попробуйте еще раз или свяжитесь с нами напрямую", "retry": "Попробовать снова"}, "footer": "ООО «Юнистат» © Москва"}, "faq": {"feedback": "Ответ был полезен?"}, "faq_list": [{"answer": "Обычно, результаты теста приходят в течение 1 дня. Иногда обработка теста может заняться до 5 дней. Следуйте инструкции при прохождении теста, чтобы уменьшить вероятность отказа.", "title": "Сколько времени обрабатывается тест? Когда я получу результаты?"}, {"answer": "Есть несколько способов:<br /><ul><li>Отправить приглашение на подключение представителя в приложении игрока</li><li>Зарегистрироваться как представитель — <a href=\"https://app.junistat.com/sign-up?role=mentor\">app.junistat.com/sign-up?role=mentor</a> и отправить приглашение игроку</li><li>Если игрок зарегистрирован через академию, попросить сотрудника академии добавить вас, как представителя</li></ul><div class=\"faq__gallery\"><img src=\"/img/faq/ru/rep-1-ru.webp\"/><img src=\"/img/faq/ru/rep-2-ru.webp\"/><img src=\"/img/faq/ru/rep-3-ru.webp\"/><img src=\"/img/faq/ru/rep-4-ru.webp\"/><img src=\"/img/faq/ru/rep-5-ru.webp\"/></div>", "title": "Как подключить представителя игроку?"}, {"answer": "Оплатить подписку можно только в кабинете представителя.<br /><ol><li>Войти в кабинет представителя <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor</a></li><li>Выбрать игрока, которому хотите подключить подписку</li><li>Нажать «Оформить подписку»</li></ol><div class=\"faq__gallery\"><img src=\"/img/faq/ru/pay-ru-1.webp\"/><img src=\"/img/faq/ru/pay-ru-2.webp\"/><img src=\"/img/faq/ru/pay-ru-3.webp\"/></div>", "title": "Как оплатить подписку?"}, {"answer": "Вы можете следить за результатами игрока в своём кабинете представителя —  <a href=\"https://app.junistat.com/login?role=mentor\">app.junistat.com/login?role=mentor</a> или в общем рейтинге на сайте, если профиль игрока открыт — <a href=\"https://junistat.com/\">junistat.com</a>", "title": "Как отслеживать результаты своего ребенка?"}, {"answer": "<ul><li>Со временем результаты становятся менее актуальными. Проходите тесты регулярно, чтобы поддерживать ваш максимальный рейтинг.</li> <li>Улучшается средние результаты тестов. Появляются новые игроки с показателями лучше, чем у вас, соответственно понижается ваш рейтинг.</li> <li>Ухудшились результаты игрока.</li></ul>", "title": "Почему уменьшается рейтинг?"}, {"answer": "<ol><li>Войти в кабинет представителя <a href='https://app.junistat.com/login?role=mentor'>app.junistat.com/login?role=mentor</a></li><li>Выбрать игрока для которого добавите видео или описание.</li> <li>В меню выбрать вкладку «О себе»</li><li>Добавьте описание или загрузите видео</li><li>Нажмите \"Сохранить\"</li></ol><div class=\"faq__gallery is--1col\"><video controls><source src='/img/faq/ru/description-ru-1.mp4' type='video/mp4'></video></div>", "title": "Как добавить видео и описание игрока?"}, {"answer": "<ol><li>Войти в кабинет представителя <a href='https://app.junistat.com/login?role=mentor'>app.junistat.com/login?role=mentor</a></li><li>Нажмите кнопку «Объединить профили»</li><li>Выберите профили игрока из академии и приложения JuniStat</li></ol>", "title": "Как объединить 2 одинаковых профиля в один?"}, {"answer": "Подписка открывает доступ ко всем тестам. Возможность сделать игрока открытым для просмотра академиями и клубами.", "title": "Для чего нужна подписка?"}, {"answer": "Вы можете зарегистрироваться в приложении JuniStat, проходить тесты и возможно, кто-то из скаутов, академий, клубов вас заметит и пригласит к себе.", "title": "Как попасть на просмотр в клубы и академии? Как вступить в академию?"}, {"answer": "<ol><li>Войти в кабинет представителя <a href='https://app.junistat.com/login?role=mentor'>app.junistat.com/login?role=mentor</a></li><li>Выбрать игрока</li><li>На странице выбранного игрока перейти в раздел «Настройки»</li><li>В конце страницы нажать кнопку «Отвязать игрока»</li></ol><div class=\"faq__gallery is--1col\"><video controls><source src='/img/faq/ru/unlink-ru-1.mp4' type='video/mp4'></video></div>", "title": "Как отвязать представителя от игрока?"}, {"answer": "Напишите нам на email <a href=\"mailto:<EMAIL>\"><EMAIL></a>. Укажите имя, номер телефона или email игрока.", "title": "Как удалить аккаунт?"}, {"answer": "Чтобы сделать это, у игрока не должно быть представителя. Попросите действующего представителя отвязать игрока от своего профиля.", "sub_answers": [{"answer": "<h4>Отправив email представителю</h4><ol><li>Войти в кабинет академии <a href=\"https://app.junistat.com/login?role=academy\">app.junistat.com/login?role=academy</a></li><li>В списке игроков нажать «Пригласить представителя»</li><li>Ввести email представителя и нажать «Отправить запрос»</li></ol><br /><h4>Поделиться профилем</h4><ol><li>Войти в кабинет академии <a href=\"https://app.junistat.com/login?role=academy\">app.junistat.com/login?role=academy</a></li><li>В списке игроков или на странице игрока нажать \"Поделиться\"</li><li>Отправить ссылку представителю любым способом.</li><li>Представитель должен зарегистрироваться по этой ссылке.</li></ol>", "title": "В кабинете академии"}, {"answer": "<ol><li>Открыть список игроков</li> <li>Напротив игрока нажать иконку «приглашение представителя»</li> <li>Указать email представителя и нажать «Отправить»</li></ol>", "title": "В приложении тренера — JuniCoach"}], "title": "Как отправить приглашение представителю?", "type": "academy"}, {"answer": "Обратитесь к текущему представителю с просьбой отменить привязку игрока к своему профилю. Когда игрок освободится от представителя, вы сможете пригласить нового.", "title": "Как заменить представителя игрока?", "type": "academy"}], "meta": {"description": "Приложения для юных футболистов, тренеров и клубов. Международный рейтинг, тестирование, выявление талантов и скаутинг"}, "navigation": {"about": "О компании", "clubs": "Клубы, тренеры, скауты", "faq": "FAQ", "parents": "Родители и представители", "press_kit": "Пресс-кит", "privacy": "Политика конфиденциальности", "rating": "<PERSON>ей<PERSON>инг", "rating_system": "Система рейтинга", "scouting": "Цифровой скаутинг", "sponsors": "Спонсоры", "team": "Команда"}, "parents": {"about_cards": "Персональную карточку, цифровой профиль и рейтинг в международной базе юных футболистов", "about_skills_subtitle": "Возможность соревноваться и сравнивать себя со сверстниками по всему миру", "about_skills_title": "Объективную оценку футбольных навыков и рекомендации по развитию", "app_for_player": "Установить приложение для игрока", "chart_text": "Данные технической и физической подготовки юного футболиста, биография и видео-нарезки его игр собираются в цифровом профиле. В зависимости от своего рейтинга и достижений, игрок может получить приглашение на просмотр в клубы", "create_parent_profile": "Создать профиль представителя", "title": "JuniStat{copyright} открывает юные футбольные таланты"}, "pricing": {"from_3000": "от 3 000 игроков", "from_3000_price_month": "{currency}4/месяц", "from_3000_price_year": "{currency}40/год", "license": {"point1": "Безлимитный доступ к тестированиям в мобильных приложениях JuniStat и JuniCoach", "point2": "Цифровые карточки и профили игроков с объективной оценкой навыков", "point3": "Динамика развития, сравнение игроков и история прогресса (цифровой трек)", "point4": "Рейтинги игроков в международной базе юных футболистов", "point5": "Рекомендации по развитию игрокам и тренерам", "point6": "Личный кабинет Академии со статистикой, аналитикой, видео и отчётами", "title": "Включено в лицензию"}, "price_player": "Цена за игрока", "price_player_description": "1 игрок = 1 лицензия", "title": "Тарифы для организаций", "up_to_1000": "до 1 000 игроков", "up_to_1000_price_month": "{currency}5/месяц", "up_to_1000_price_year": "{currency}50/год", "up_to_3000": "от 1,000 до 3,000 игроков", "up_to_3000_price_month": "{currency}4.5/месяц", "up_to_3000_price_year": "{currency}45/год"}, "rating": {"banner": {"subtitle": "Скачай приложение и пройди тесты", "title": "Получи свой рейтинг"}, "card_clubs": "Решение для клубов", "card_parents": "Родителям и представителям", "example": {"point1": "Персональная карточка с объективной оценкой навыков", "point2": "Цифровой профиль c историей развития навыков", "point3": "Рейтинг в международной базе талантов", "point4": "Шанс попасть в клуб или академию", "title": "Доступно для каждого игрока"}, "filters": "Фильтры", "grid": {"error": "Ошибка загрузки игроков. Обновите страницу или попробуйте позже", "next": "Вперёд", "prev": "Назад", "empty": "Игроки не найдены или скрыты"}, "popup": {"button_login": "Войти", "button_signUp": "Регистрация", "description": "Чтобы получить полный доступ к базе игроков и расширенному поиску", "title": "Создайте аккаунт бесплатно"}, "search_placeholder": "Имя игрока", "select_club": "Выберите клуб", "titleAllPlayers": "Игроки", "title_players_month": "Игроки месяца", "subtitle_players_month": "Пройди тесты и войди в ТОП лучших футболистов месяца. Учитываются результаты тестов и активность", "scout": {"title": "Вы скаут, агент или представитель футбольной академии?", "text": "Получайте ежемесячную аналитическую записку и инсайты о лучших игроках, прошедших наши тесты. Найди будущих звезд футбола вместе с JuniStat", "button": "Подписаться", "terms": "Нажимая кнопку «Подписаться», вы соглашаетесь с <a href='https://junistat.com/privacy/' target='_blank'>Политикой конфиденциальности</a>", "form-success": "Спасибо за подписку. Вы получите наш следующий email с лучшими игроками месяца", "form-error": "Произошла ошибка. Пожалуйста, попробуйте ещё раз. Если проблема остаётся, сообщите нам по адресу <EMAIL>"}}, "sign_up": "Войти", "Top 5 Startups 2025": "ТОП 5 стартапов 2025", "sponsors": {"contact": "Контактное лицо - Глеб Шапортов", "text": "Сервисом пользуются более 100 000 юных футболистов по всему миру. Новые таланты регистрируются каждый день, но не все платежеспособны. Спонсоры могут помочь игрокам оплачивать подписку на сервис. На карточках игроков будет размещен логотип спонсора в течение всего времени действия подписки. Доступны пакеты от 100 подписок.", "title": "Помогите молодым игрокам получить шанс пробиться в футболе!"}, "team": {"ambassador_title": "Амбассадоры", "how_become_ambassador_text": "Если вы хотите представлять JuniStat на территории своей страны, напишите на почту —", "how_become_ambassador_title": "Как стать нашим Амбассадором?", "team_title": "Управление и разработка"}, "delete_account": {"title": "Запрос на удаление аккаунта", "description": "Ваша учетная запись и вся связанная с ней информация будут удалены в течение 48 часов", "button": "Отправить запрос", "error": "Ошибка при отправке запроса. Пожалуйста, попробуйте повторить позже. Если проблема остаётся, сообщите нам об этом по адресу <EMAIL>", "success": "Ваш запрос получен. Учетная запись и вся связанная с ней информация будут удалены"}, "help_center": {"back_button": "← Все инструкции", "title": "Помощь"}, "blog": {"title": "Блог"}}