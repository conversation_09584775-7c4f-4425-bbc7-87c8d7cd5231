---
import { languages } from "src/lang/langs";
import {
  getLangFromUrl,
  useTranslations,
  useTranslatedPath,
  changeLang,
} from "src/lang/utils";

//#region [i18n]
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);
//#endregion [i18n]

const currentLang = getLangFromUrl(Astro.url);

interface Props {
  class?: string;
  langArray?: string[];
}

const { class: className, langArray } = Astro.props;

function createTransaltedLink(lang: string) {
  const currentURL = Astro.url;
  return changeLang(lang, true, currentURL);
}
---

<select
  id="lang-selector"
  class:list={[className]}
  aria-label="Select language"
>
  {
    Object.entries(langArray || languages).map(([lang, label]) => {
      const isSelected = lang === currentLang ? { selected: "selected" } : "";

      return (
        <option value={lang} {...isSelected}>
          {label}
        </option>
      );
    })
  }
</select>

{
  Object.entries(languages).map(([lang, label]) => {
    return (
      <li style="display: none; list-style: none">
        <a
          lang={lang}
          hreflang={lang}
          href={createTransaltedLink(lang)}
          role="option"
          data-value={label}
          class="language-picker__item language-picker__flag language-picker__flag--deutsch"
        >
          {label}
        </a>
      </li>
    );
  })
}

<script>
  import { track } from "@amplitude/analytics-browser";

  import { languages, defaultLang } from "src/lang/langs";
  import { getLangFromUrl, changeLang } from "src/lang/utils";

  const selectors = document.querySelectorAll(
    "#lang-selector",
  ) as NodeListOf<HTMLElement>;

  selectors.forEach((selector: HTMLElement) => {
    selector.addEventListener("change", (e: Event) => {
      const selectElement = e.target as HTMLSelectElement;
      track("Change language", {
        current_lang: getLangFromUrl(window.location),
        new_lang: selectElement.value,
      });

      localStorage.setItem("lang", selectElement.value);

      changeLang(selectElement.value);
    });
  });
</script>
