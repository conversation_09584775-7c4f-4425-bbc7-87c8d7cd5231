---
import Tests from "./testsResults.js";

import TestResultItem from "./TestResultItem.astro";

interface Props {
  testName: string;
  lang?: "ru" | "en";
}

const { testName, lang } = Astro.props;
---

<div class="result-list">
  {
    Tests[testName].map((result) => (
      <TestResultItem
        lang={lang}
        propLabel={result.propLabel}
        value={result.value}
        benchmarkValue={result.benchmarkValue}
        playerBarPosition={result.playerBarPosition}
        average={result.average}
      />
    ))
  }
</div>

<style>
  .result-list {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    border: #dfe5ea solid 1px;
    padding-bottom: 16px;
  }

  .result-list :global(> * + *) {
    margin-top: 16px;
    border-top: #dfe5ea solid 1px;
  }
</style>
