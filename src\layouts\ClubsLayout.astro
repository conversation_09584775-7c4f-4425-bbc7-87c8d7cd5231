---
import "@styles/main.scss";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
//#endregion [i18n]

import Layout from "@layouts/Layout.astro";

//#region [Styles]
import "swiper/css";
//#endregion [Styles]

//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import AppButton from "@components/AppButton.astro";
import DownloadButtons from "@components/DownloadButtons.astro";
import CardProfileExample from "@components/CardProfileExample.astro";
import SkillsCard from "@components/SkillsCard.astro";
//#endregion [Components]

//#region [Styles]
import "@styles/pages/clubs.scss";
//#endregion [Styles]

//#region [Icons]
import RocketLaunch from "@components/icons/RocketLaunch.astro";
//#endregion [Icons]

//#region [Images]
import videoCoachPoster from "@assets/videos/coach/coach-cover.jpg";

// How
import ConesImg from "@assets/images/clubs/how/cones.jpg";
import PhoneImg from "@assets/images/clubs/how/phone.jpg";
import TapeImg from "@assets/images/clubs/how/tape.jpg";
import TripodImg from "@assets/images/clubs/how/tripod.jpg";
import EquipPhotoImg from "@assets/images/clubs/how/equip-photo.jpg";

// Exercises
const SkillsImagesRu = await Astro.glob(
  "/src/assets/images/skills-cards/ru/*.{png,svg,jpg}"
).then((files) => {
  return files.map((file) => {
    return file.default;
  });
});
const SkillsImagesEn = await Astro.glob(
  "/src/assets/images/skills-cards/en/*.{png,svg,jpg}"
).then((files) => {
  return files.map((file) => {
    return file.default;
  });
});

const SkillsImages = lang !== "ru" ? SkillsImagesEn : SkillsImagesRu;

import LavricImg from "@assets/images/clubs/vasile-lavric_psg-min.jpg";

// Features
import CardGrilImg from "@assets/images/clubs/card-girl.png";
import PlayerVideoImg1 from "@assets/images/clubs/features/player-video-01.jpg";
import PlayerVideoImg2 from "@assets/images/clubs/features/player-video-02.jpg";
import PlayerVideoImg3 from "@assets/images/clubs/features/player-video-03.jpg";

// Skills cards
const skills: Array<Array<{ [key: string]: [string, number] }>> = [
  [
    { "Max. speed": ["5.98 m/sec", 70] },
    { "Sprint time 10m": ["2.01 sec.", 95] },
    { "Sprint time 15m": ["2.82 sec.", 56] },
    { "Starting speed": ["4.62 m/sec", 80] },
    { "Feet speed": ["38 unit.", 85] },
  ],
  [
    { Reaction: ["0.53 sec.", 70] },
    { "Direction change": ["11.43 sec.", 95] },
    { Coordination: ["19 unit.", 56] },
    { "Starting speed": ["4.62 m/sec", 80] },
    { Balance: ["1.03 sec.", 85] },
  ],
  [
    { "Speed dribbling": ["21.77 sec.", 70] },
    { "Ball control RF": ["66.30", 95] },
    { "Ball control LF": ["62.90", 56] },
    { "Dribbling RF": ["15.14 sec.", 80] },
    { "Dribbling L": ["16.54 sec.", 85] },
  ],
  [
    { "Kick power RF": ["77.73 km/h", 70] },
    { "Kick power LF": ["100.38 km/h", 95] },
  ],
  [{ Strength: ["38.02", 70] }, { Jumping: ["0.51 m", 95] }],
];

// Steps
import StepsBgImageRaw from "@assets/images/clubs/steps-bg.jpg";
const StepsBgImage = await getImage({ src: StepsBgImageRaw, format: "webp" });

// Slider Recommends
interface RecommendItem {
  src: string;
  width: number;
  height: number;
  format: string;
}

interface SortedRecommendsLetters {
  [key: string]: RecommendItem;
}
const recommendsLetters: ImageMetadata[] = await Astro.glob(
  "/src/assets/images/clubs/recommends/*"
).then((files) => {
  return files
    .map((file) => {
      if (lang !== "ru" && !file.default.src.includes(`-ru.`)) {
        return file.default;
      } else if (lang === "ru") {
        return file.default;
      }
    })
    .filter((file) => typeof file === "object");
});

// Function to sort the object based on the condition
const sortArray = (arr: ImageMetadata[]): ImageMetadata[] => {
  return arr.sort((a, b) => {
    const aIncludesRfs = a.src.includes("rfs-");
    const bIncludesRfs = b.src.includes("rfs-");

    if (aIncludesRfs && !bIncludesRfs) {
      return -1; // a comes first
    } else if (!aIncludesRfs && bIncludesRfs) {
      return 1; // b comes first
    } else {
      return 0; // no change in order
    }
  });
};

// Sort the object
const sortedRecommends = sortArray(recommendsLetters);

import IanAvatar from "@assets/images/team/ian.jpg";
//#endregion [Images]

//#region [Videos]
import coachVideoWebM from "@assets/videos/coach/coach.webm";
import coachVideoMp4 from "@assets/videos/coach/coach.mp4";
//#endregion [Videos]
---

<Layout>
  <!-- Header -->
  <section id="hero">
    <div class="container">
      <div class="hero">
        <div class="hero__top">
          <div class="hero__text-side">
            <h1 class="hero__headline">
              {t("clubs.hero.title")}
            </h1>
            <div class="hero__subtitle h3">
              {t("clubs.hero.subtitle")}
            </div>
          </div>
          <div class="hero__cta-side">
            <div class="hero__comparison-wrap">
              <div class="hero__comparison-point">
                <p class="hero__comparison-point-text">
                  <span set:html={t("clubs.hero.points.0.we")} />
                  <span class="hero__comparison-point-text-vs"> vs.</span>
                  <span> {t("clubs.hero.points[0].they")}</span>
                </p>
              </div>
              <div class="hero__comparison-point">
                <p class="hero__comparison-point-text">
                  <span> {t("clubs.hero.points.1.we")}</span>
                  <span class="hero__comparison-point-text-vs"> vs.</span>
                  <span> {t("clubs.hero.points.1.they")}</span>
                </p>
              </div>
            </div>
            <div class="hero__cta-button-wrap">
              <p class="hero__cta-button-description">
                {t("clubs.hero.reg_description")}
              </p>
              <AppButton
                as="a"
                typeStyle="primary"
                id="club-register"
                style="mod2"
                href="https://app.junistat.com/welcome"
                target="_blank"
              >
                <div class="hero__cta-button-icon">
                  <RocketLaunch />
                </div>
                <div class="button-label h3">
                  {t("clubs.hero.reg_button")}
                </div>
              </AppButton>

              <AppButton
                class:list={["hero__request-presentation-button"]}
                as="button"
                typeStyle="secondary"
              >
                <div class="hero__cta-button-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="100%"
                    height="100%"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                    ><path
                      d="M224,152a8,8,0,0,1-8,8H192v16h16a8,8,0,0,1,0,16H192v16a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8h32A8,8,0,0,1,224,152ZM92,172a28,28,0,0,1-28,28H56v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8H64A28,28,0,0,1,92,172Zm-16,0a12,12,0,0,0-12-12H56v24h8A12,12,0,0,0,76,172Zm88,8a36,36,0,0,1-36,36H112a8,8,0,0,1-8-8V152a8,8,0,0,1,8-8h16A36,36,0,0,1,164,180Zm-16,0a20,20,0,0,0-20-20h-8v40h8A20,20,0,0,0,148,180ZM40,112V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88v24a8,8,0,0,1-16,0V96H152a8,8,0,0,1-8-8V40H56v72a8,8,0,0,1-16,0ZM160,80h28.69L160,51.31Z"
                    ></path></svg
                  >
                </div>
                <div class="button-label h4">
                  {t("clubs.hero.request_presentation")}
                </div>
              </AppButton>
            </div>
          </div>
        </div>
        <div class="hero__video video">
          <svg
            class="video__play-button"
            xmlns="http://www.w3.org/2000/svg"
            width="80"
            height="80"
            fill="none"
            viewBox="0 0 80 80"
            ><path
              fill="#fff"
              d="M40 5C20.672 5 5 20.672 5 40s15.672 35 35 35 35-15.672 35-35S59.328 5 40 5Zm0 64.063c-16.047 0-29.063-13.016-29.063-29.063 0-16.047 13.016-29.063 29.063-29.063 16.047 0 29.063 13.016 29.063 29.063 0 16.047-13.016 29.063-29.063 29.063Z"
            ></path><path
              fill="#fff"
              d="M50.744 39.494 33.975 27.12a.609.609 0 0 0-.885.18.63.63 0 0 0-.09.326v24.751a.633.633 0 0 0 .335.555.608.608 0 0 0 .64-.048L50.744 40.5a.613.613 0 0 0 .256-.502.623.623 0 0 0-.256-.503Z"
            ></path></svg
          >
          <video
            class="hero__video-tag"
            poster={videoCoachPoster.src}
            loop
            style="margin-bottom: -1rem"
            muted
            playsinline
          >
            <source src={coachVideoWebM} />
            <source src={coachVideoMp4} />
          </video>
        </div>
      </div>
    </div>
  </section>

  <!-- Presentation modal -->
  <div
    class="presentation-modal"
    role="dialog"
    aria-modal="true"
    aria-labelledby="presentation-modal__title"
  >
    <div class="presentation-modal__container">
      <div class="presentation-modal__header">
        <div
          id="presentation-modal__title"
          class="presentation-modal__title h3"
        >
          {t("clubs.hero.request_presentation")}
        </div>
        <button class="presentation-modal__close"
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="currentColor"
            viewBox="0 0 256 256"
            ><path
              d="M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"
            ></path></svg
          ></button
        >
      </div>
      <div class="presentation-modal__content">
        <form action="" class="presentation-modal__form">
          <label class="presentation-modal__input-wrap">
            <span class="presentation-modal__label"
              >{t("clubs.hero.form.name_input")}</span
            >
            <input
              type="text"
              name="name"
              class="presentation-modal__input"
              required
              placeholder={t("clubs.hero.form.name_input")}
            />
          </label>
          <label class="presentation-modal__input-wrap">
            <span class="presentation-modal__label"
              >{t("clubs.hero.form.email_input")}</span
            >
            <input
              type="email"
              name="email"
              class="presentation-modal__input"
              required
              placeholder={t("clubs.hero.form.email_input")}
            />
          </label>
          <label class="presentation-modal__input-wrap">
            <span class="presentation-modal__label"
              >{t("clubs.hero.form.phone_input")}</span
            >
            <input
              type="text"
              name="phone"
              class="presentation-modal__input"
              required
              placeholder={t("clubs.hero.form.phone_input")}
            />
          </label>
          <label class="presentation-modal__input-wrap">
            <span class="presentation-modal__label"
              >{t("clubs.hero.form.academy_input")}</span
            >
            <input
              type="text"
              name="academy"
              class="presentation-modal__input"
              required
              placeholder={t("clubs.hero.form.academy_input")}
            />
          </label>
          <AppButton
            as="button"
            class:list="presentation-modal__button"
            style="mod2"
          >
            <span class="h5">{t("clubs.hero.form.send_button")}</span>
          </AppButton>
          <span class="presentation-modal__legal-text text-small">
            {t("clubs.hero.form.legal_text")}<a
              class="text-link"
              href="/privacy"
              target="_blank">{t("clubs.hero.form.legal_text_privacy")}</a
            >
          </span>
        </form>
        <div class="form__error">
          {t("clubs.hero.form.form_error")}
        </div>
        <div class="form__success">
          {t("clubs.hero.form.form_success")}
        </div>
      </div>
    </div>
  </div>

  <!-- How it works -->
  <section id="how" class="section-space">
    <div class="container">
      <div class="how">
        <h2 class="how__headline">
          {t("clubs.how.title")}
        </h2>
        <div class="how__points">
          <div class="how__column">
            <div class="how__point-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="100%"
                height="100%"
                fill="none"
                viewBox="0 0 64 64"
              >
                <path
                  fill="#FFE83F"
                  d="M49.5 18v28a1.5 1.5 0 0 1-1.5 1.5H8A1.5 1.5 0 0 1 6.5 46V18A1.5 1.5 0 0 1 8 16.5h40a1.5 1.5 0 0 1 1.5 1.5Z"
                ></path><path
                  fill="url(#a)"
                  d="M49.5 18v28a1.5 1.5 0 0 1-1.5 1.5H8A1.5 1.5 0 0 1 6.5 46V18A1.5 1.5 0 0 1 8 16.5h40a1.5 1.5 0 0 1 1.5 1.5Z"
                ></path><path
                  stroke="#DFE5EA"
                  d="M49.5 18v28a1.5 1.5 0 0 1-1.5 1.5H8A1.5 1.5 0 0 1 6.5 46V18A1.5 1.5 0 0 1 8 16.5h40a1.5 1.5 0 0 1 1.5 1.5Z"
                ></path><path
                  fill="#181B1E"
                  d="M62.943 18.25a2 2 0 0 0-2.053.098L52 24.262V18a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v28a4 4 0 0 0 4 4h40a4 4 0 0 0 4-4v-6.25l8.89 5.928A2 2 0 0 0 62 46a2 2 0 0 0 2-2V20a2 2 0 0 0-1.057-1.75ZM48 46H8V18h40v28Zm12-5.737-8-5.333v-5.86l8-5.32v16.513Z"
                ></path><defs
                  ><linearGradient
                    id="a"
                    x1="28"
                    x2="28"
                    y1="16"
                    y2="48"
                    gradientUnits="userSpaceOnUse"
                    ><stop stop-color="#FFE83F"></stop><stop
                      offset="1"
                      stop-color="#FFD53F"></stop></linearGradient
                  ></defs
                >
              </svg>
            </div>
            <h3 class="how__point-title">
              {t("clubs.how.point1.title")}
            </h3>
            <DownloadButtons app="coach" place="How it works" />
          </div>
          <div class="how__column">
            <div class="how__point-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="100%"
                height="100%"
                fill="none"
                viewBox="0 0 64 64"
              >
                <path
                  fill="#FFE83F"
                  d="M55.5 14v30a1.5 1.5 0 0 1-1.5 1.5H10A1.5 1.5 0 0 1 8.5 44V14a1.5 1.5 0 0 1 1.5-1.5h44a1.5 1.5 0 0 1 1.5 1.5Z"
                ></path><path
                  fill="url(#a)"
                  d="M55.5 14v30a1.5 1.5 0 0 1-1.5 1.5H10A1.5 1.5 0 0 1 8.5 44V14a1.5 1.5 0 0 1 1.5-1.5h44a1.5 1.5 0 0 1 1.5 1.5Z"
                ></path><path
                  stroke="#DFE5EA"
                  d="M55.5 14v30a1.5 1.5 0 0 1-1.5 1.5H10A1.5 1.5 0 0 1 8.5 44V14a1.5 1.5 0 0 1 1.5-1.5h44a1.5 1.5 0 0 1 1.5 1.5Z"
                ></path><path
                  fill="#181B1E"
                  d="M54 10H34V6a2 2 0 1 0-4 0v4H10a4 4 0 0 0-4 4v30a4 4 0 0 0 4 4h9.84l-5.402 6.75a2.001 2.001 0 0 0 3.124 2.5L24.96 48h14.08l7.398 9.25a2.002 2.002 0 0 0 3.124-2.5L44.16 48H54a4 4 0 0 0 4-4V14a4 4 0 0 0-4-4Zm0 34H10V14h44v30ZM26 30v6a2 2 0 0 1-4 0v-6a2 2 0 0 1 4 0Zm8-4v10a2 2 0 0 1-4 0V26a2 2 0 0 1 4 0Zm8-4v14a2 2 0 0 1-4 0V22a2 2 0 0 1 4 0Z"
                ></path><defs
                  ><linearGradient
                    id="a"
                    x1="32"
                    x2="32"
                    y1="12"
                    y2="46"
                    gradientUnits="userSpaceOnUse"
                    ><stop stop-color="#FFE83F"></stop><stop
                      offset="1"
                      stop-color="#FFD53F"></stop></linearGradient
                  ></defs
                >
              </svg>
            </div>
            <h3 class="how__point-title">
              {t("clubs.how.point2.title")}
            </h3>
          </div>
        </div>
      </div>
      <div class="how__equip">
        <h3 class="how__equip-title">
          {t("clubs.how.equip")}
        </h3>
        <div class="how__equip-content">
          <div class="how__equip-col is--items">
            <div class="how__equip-item">
              <Image
                class="how__equip-item-img"
                src={PhoneImg}
                alt="Smartphone"
              />
              <p>
                {t("clubs.how.equip_item.phone")}
              </p>
            </div>
            <div class="how__equip-item">
              <Image class="how__equip-item-img" src={TripodImg} alt="Tripod" />
              <p>
                {t("clubs.how.equip_item.tripod")}
              </p>
            </div>
            <div class="how__equip-item">
              <Image class="how__equip-item-img" src={ConesImg} alt="Cones" />
              <p>{t("clubs.how.equip_item.cones")}</p>
            </div>
            <div class="how__equip-item">
              <Image
                class="how__equip-item-img"
                src={TapeImg}
                alt="Measure tape"
              />
              <p>
                {t("clubs.how.equip_item.tape")}
              </p>
            </div>

            <AppButton
              as="a"
              id="trial-access-button"
              typeStyle="primary"
              style="mod2"
              href="https://app.junistat.com/welcome"
              class="how__equip-button"
              target="_blank"
            >
              <RocketLaunch class="hero__cta-button-icon" />
              <h3>
                {t("clubs.how.free_trial")}
              </h3>
            </AppButton>
          </div>
          <div class="how__equip-col is--img">
            <Image
              class="how__equip-photo"
              src={EquipPhotoImg}
              alt="Coach tests Antalyaspor team via JuniCoach"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Exercises -->
  <section id="exercises" class="section-space">
    <div class="container is--exercises">
      <div class="exercises">
        <div class="exercises__header">
          <h3 class="exercises__title">
            {t("clubs.exercises.title")}
          </h3>

          <div class="recommends-people__card">
            <div class="recommends-people__card-text">
              {t("clubs.quote")}
            </div>
            <div class="recommends-people__card-author">
              <Image
                class="recommends-people__card-author-img"
                src={LavricImg}
                alt="Vasile Lavrice"
              />
              <div class="recommends-people__card-author-info">
                Vasile Lavrice <br />
                PSG
              </div>
            </div>
          </div>
        </div>
        <div class="tests__images">
          <Image class="tests__img" src={SkillsImages[0]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[3]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[1]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[4]} alt="Skill card" />
          <Image class="tests__img" src={SkillsImages[2]} alt="Skill card" />
        </div>
      </div>
    </div>
  </section>
  <!-- Features -->
  <section id="features" class="section-space">
    <div class="container">
      <div class="features">
        <div class="features__card-profile-wrap">
          <CardProfileExample
            flag="ca"
            example-card-flag="custom"
            class="features__card-profile"
          />
          <Image
            class="features__card-girl"
            src={CardGrilImg}
            alt="Example of JuniStat profile card"
          />
        </div>
        <div class="features__blocks">
          <div class="features__profile-description">
            <h2 class="features__profile-title">
              {t("clubs.features.title")}
            </h2>
            <h3 class="features__profile-point">
              {t("clubs.features.card.title")}
            </h3>
            <h3 class="features__profile-point">
              {t("clubs.features.card.description")}
            </h3>
          </div>
          <div class="features__skills">
            <h2 class="features__skills-title">
              {t("clubs.features.results.title")}
            </h2>
            <div class="features__skills-grid">
              {
                [
                  { name: "Pace", points: 89 },
                  { name: "Agility", points: 67 },
                  { name: "Dribbling", points: 58 },
                  { name: "Shooting", points: 84 },
                  { name: "Physical", points: 80 },
                ].map((skill, i) => {
                  return (
                    <SkillsCard
                      skills={skills[i]}
                      skillCardName={skill.name}
                      skillCardPoints={skill.points}
                    />
                  );
                })
              }
            </div>
          </div>
          <div class="features__videos">
            <h2 class="features__videos-title">
              {t("clubs.features.video.title")}
            </h2>
            <div class="features__videos-wrap">
              <div class="features__video-wrap">
                <svg
                  class="features__video-play-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="33"
                  height="30"
                  fill="none"
                  viewBox="0 0 33 30"
                  ><path
                    fill="#fff"
                    d="M31.244 13.097c1.563.71 1.563 2.931 0 3.642L2.828 29.654A2 2 0 0 1 0 27.834V2.001A2 2 0 0 1 2.828.182l28.416 12.915Z"
                  ></path>
                </svg>
                <Image
                  class="features__video-img"
                  src={PlayerVideoImg1}
                  alt="Player's video"
                />
              </div>
              <div class="features__video-wrap">
                <svg
                  class="features__video-play-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="33"
                  height="30"
                  fill="none"
                  viewBox="0 0 33 30"
                  ><path
                    fill="#fff"
                    d="M31.244 13.097c1.563.71 1.563 2.931 0 3.642L2.828 29.654A2 2 0 0 1 0 27.834V2.001A2 2 0 0 1 2.828.182l28.416 12.915Z"
                  ></path>
                </svg>
                <Image
                  class="features__video-img"
                  src={PlayerVideoImg2}
                  alt="Player's video"
                />
              </div>
              <div class="features__video-wrap">
                <svg
                  class="features__video-play-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="33"
                  height="30"
                  fill="none"
                  viewBox="0 0 33 30"
                  ><path
                    fill="#fff"
                    d="M31.244 13.097c1.563.71 1.563 2.931 0 3.642L2.828 29.654A2 2 0 0 1 0 27.834V2.001A2 2 0 0 1 2.828.182l28.416 12.915Z"
                  ></path>
                </svg>
                <Image
                  class="features__video-img"
                  src={PlayerVideoImg3}
                  alt="Player's video"
                />
              </div>
            </div>
          </div>
          <div class="features__data">
            <h2 class="features__data-title">
              {t("clubs.features.data.title")}
            </h2>
            <div class="features__data-wrap">
              <div class="features__params-list">
                <div class="features__param">
                  <div class="h4 features__param-name">
                    {t("clubs.features.data.height")}
                  </div>
                  <div class="h4 features__param-value">142</div>
                </div>
                <div class="features__param">
                  <div class="h4 features__param-name">
                    {t("clubs.features.data.weight")}
                  </div>
                  <div class="h4 features__param-value">50</div>
                </div>
                <div class="features__param">
                  <div class="h4 features__param-name">
                    {t("clubs.features.data.position")}:
                  </div>
                  <div class="h4 features__param-value">
                    {t("clubs.features.data.position_value")}
                  </div>
                </div>
              </div>
              <p class="features__data-description p2">
                {t("clubs.features.data.description_text")}
              </p>
            </div>
            <div class="features__data-lock">
              <div class="features__data-lock-circle">
                <svg
                  class="features__data-lock-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  fill="none"
                  viewBox="0 0 32 32"
                >
                  <path
                    fill="#121212"
                    d="M26 11H6a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h20a1 1 0 0 0 1-1V12a1 1 0 0 0-1-1Zm-10 9a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5Z"
                    opacity=".2"></path><path
                    fill="#121212"
                    d="M26 10H12V7a4 4 0 0 1 4-4c1.921 0 3.65 1.375 4.02 3.199a1 1 0 0 0 1.96-.398C21.415 3.02 18.9 1 16 1a6.006 6.006 0 0 0-6 6v3H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V12a2 2 0 0 0-2-2Zm0 16H6V12h20v14ZM16 14a3.5 3.5 0 0 0-1 6.854V23a1 1 0 0 0 2 0v-2.146A3.5 3.5 0 0 0 16 14Zm0 5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Z"
                  ></path>
                </svg>
              </div>
              <p class="features__data-lock-text p2">
                {t("clubs.profile_protect_text")}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Steps -->
  <section
    id="steps"
    class="section-space"
    style=`
  background: linear-gradient( 180deg, rgba(11, 21, 32, 0.61) 0%, rgba(11, 21, 32, 0.9) 100%),center / cover no-repeat url(${StepsBgImage.src});
  `
  >
    <div class="container">
      <h2 class="steps__headline">
        {t("clubs.steps.title")}
      </h2>
      <div class="steps-grid">
        <div class="steps__step-card">
          <div class="steps__step-icon-wrap">
            <svg
              class="steps__step-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              fill="none"
              viewBox="0 0 32 32"
            >
              <path
                fill="#181B1E"
                d="M28 16a12 12 0 1 1-23.999 0A12 12 0 0 1 28 16Z"
                opacity=".2"></path><path
                fill="#181B1E"
                d="M16 3a13 13 0 1 0 13 13A13.013 13.013 0 0 0 16 3Zm-3.296 18h6.592c-.671 2.293-1.796 4.359-3.296 5.986-1.5-1.627-2.625-3.694-3.296-5.986Zm-.454-2a18.213 18.213 0 0 1 0-6h7.5a18.213 18.213 0 0 1 0 6h-7.5ZM5 16c0-1.014.14-2.024.416-3h4.808a20.22 20.22 0 0 0 0 6H5.416A10.951 10.951 0 0 1 5 16Zm14.296-5h-6.592C13.375 8.707 14.5 6.641 16 5.014 17.5 6.64 18.625 8.707 19.296 11Zm2.48 2h4.808a11.019 11.019 0 0 1 0 6h-4.808a20.226 20.226 0 0 0 0-6Zm4.02-2h-4.428a17.799 17.799 0 0 0-2.533-5.625A11.046 11.046 0 0 1 25.796 11ZM13.165 5.375A17.799 17.799 0 0 0 10.633 11h-4.43a11.046 11.046 0 0 1 6.962-5.625ZM6.204 21h4.429a17.799 17.799 0 0 0 2.532 5.625A11.047 11.047 0 0 1 6.204 21Zm12.631 5.625A17.799 17.799 0 0 0 21.367 21h4.43a11.047 11.047 0 0 1-6.962 5.625Z"
              ></path>
            </svg>
          </div>
          <p class="steps__step-text">
            {t("clubs.steps.text1")}
          </p>
        </div>
        <div class="steps__step-card">
          <div class="steps__step-icon-wrap">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              fill="none"
              viewBox="0 0 32 32"
            >
              <path
                fill="#181B1E"
                d="M28 7v15a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h22a1 1 0 0 1 1 1Z"
                opacity=".2"></path>
              <path
                fill="#181B1E"
                d="M27 5H17V3a1 1 0 0 0-2 0v2H5a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h4.92l-2.701 3.375a1 1 0 0 0 1.562 1.25L12.48 24h7.04l3.699 4.625a1 1 0 1 0 1.562-1.25L22.08 24H27a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2Zm0 17H5V7h22v15Zm-14-7v3a1 1 0 0 1-2 0v-3a1 1 0 0 1 2 0Zm4-2v5a1 1 0 0 1-2 0v-5a1 1 0 0 1 2 0Zm4-2v7a1 1 0 0 1-2 0v-7a1 1 0 0 1 2 0Z"
              ></path>
            </svg>
          </div>
          <p class="steps__step-text">
            {t("clubs.steps.text2")}
          </p>
        </div>
        <div class="steps__step-card">
          <div class="steps__step-icon-wrap">
            <svg
              class="steps__step-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              fill="none"
              viewBox="0 0 32 32"
            >
              <path
                fill="#181B1E"
                d="M20 14a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"
                opacity=".2"></path><path
                fill="#181B1E"
                d="M28 6v3.5a1 1 0 0 1-2 0V6h-3.5a1 1 0 1 1 0-2H26a2 2 0 0 1 2 2Zm-1 15.5a1 1 0 0 0-1 1V26h-3.5a1 1 0 0 0 0 2H26a2 2 0 0 0 2-2v-3.5a1 1 0 0 0-1-1ZM9.5 26H6v-3.5a1 1 0 1 0-2 0V26a2 2 0 0 0 2 2h3.5a1 1 0 0 0 0-2ZM5 10.5a1 1 0 0 0 1-1V6h3.5a1 1 0 1 0 0-2H6a2 2 0 0 0-2 2v3.5a1 1 0 0 0 1 1ZM22 22a1 1 0 0 1-.801-.399 6.5 6.5 0 0 0-10.4 0 1 1 0 1 1-1.6-1.202 8.493 8.493 0 0 1 3.426-2.71 5 5 0 1 1 6.742 0 8.492 8.492 0 0 1 3.43 2.71A1 1 0 0 1 22 22Zm-6-5a3 3 0 1 0 0-5.999A3 3 0 0 0 16 17Z"
              ></path>
            </svg>
          </div>
          <p class="steps__step-text">
            {t("clubs.steps.text3")}
          </p>
        </div>
      </div>
      <AppButton
        as="a"
        id="connect-club-button"
        style="mod2"
        class="steps__registration-button"
        href="https://app.junistat.com/sign-up?role=academy"
        target="_blank"
      >
        <RocketLaunch class="hero__cta-button-icon" />
        <h3>{t("clubs.connect_club")}</h3>
      </AppButton>
      <p class="clubs__registration-description p2">
        {t("clubs.hero.reg_description")}
      </p>
    </div>
  </section>
  <!-- Recommends -->
  <section id="recommends" class="section-space">
    <div class="container">
      <div class="recommends">
        <h2 class="recommends__title">{t("clubs.recommends_title")}</h2>
        <div class="recommends__slider">
          <div class="recommends__slider-shadow"></div>
          <div class="swiper">
            <div class="swiper-wrapper recommends__slider-container">
              {
                sortedRecommends.map((letter: ImageMetadata) => {
                  return (
                    <div class="swiper-slide recommends__slide">
                      <a href={letter.src} target="_blank">
                        <Image src={letter} alt="Recommend letter" />
                      </a>
                    </div>
                  );
                })
              }
            </div>
          </div>
        </div>
        <div class="recommends__slider-controls">
          <div class="recommends__slider-button is--prev">
            <svg
              class="recommends__slider-button-arrow"
              xmlns="http://www.w3.org/2000/svg"
              width="100%"
              height="100%"
              fill="none"
              viewBox="0 0 56 56"
            >
              <path
                fill="currentColor"
                d="M49 28a1.75 1.75 0 0 1-1.75 1.75h-21v14a1.75 1.75 0 0 1-2.988 1.238l-15.75-15.75a1.751 1.751 0 0 1 0-2.477l15.75-15.75a1.75 1.75 0 0 1 2.988 1.238v14h21A1.75 1.75 0 0 1 49 28Z"
              ></path>
            </svg>
          </div>
          <div class="recommends__slider-button is--next">
            <svg
              class="recommends__slider-button-arrow"
              xmlns="http://www.w3.org/2000/svg"
              width="100%"
              height="100%"
              fill="none"
              viewBox="0 0 56 56"
            >
              <path
                fill="currentColor"
                d="m48.488 29.238-15.75 15.75a1.75 1.75 0 0 1-2.988-1.239v-14h-21a1.75 1.75 0 1 1 0-3.5h21v-14a1.75 1.75 0 0 1 2.988-1.238l15.75 15.75a1.75 1.75 0 0 1 0 2.477Z"
              ></path>
            </svg>
          </div>
        </div>
      </div>
      <div class="recommends-people">
        <div class="recommends-people__card is--ian">
          <div class="recommends-people__card-author">
            <Image
              class="recommends-people__card-author-img"
              src={IanAvatar}
              width="64"
              height="64"
              alt="Ian McClurg"
            />
            <div class="recommends-people__card-author-info">
              Ian McClurg <br />
              Learn perform
            </div>
          </div>
          <div class="recommends-people__card-text-wrap">
            <div
              class="recommends-people__card-text is--ian"
              set:html={`<q>${t("clubs.recommends.ian_text")}</q>`}
            />
            <AppButton
              as="button"
              typeStyle="secondary"
              class="recommends-people__card-text-expand"
            >
              <div id="ian-show-more">{t("clubs.recommends.show_more")}</div>
              <div id="ian-show-less" style="display: none">
                {t("clubs.recommends.show_less")}
              </div>
            </AppButton>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Footer cards -->
  <section id="footer-cards" class="section-space">
    <div class="container">
      <div class="footer-cards">
        <a
          class="footer-cards__card"
          id="footer-trial-access-card"
          href="https://app.junistat.com/welcome"
          target="_blank"
          style="grid-column-end: 2 span"
        >
          <svg
            class="footer-cards__card-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            ><path
              d="M8.566 17.842c-.945 2.462-3.678 4.012-6.563 4.161.139-2.772 1.684-5.608 4.209-6.563l.51.521c-1.534 1.523-2.061 2.765-2.144 3.461.704-.085 2.006-.608 3.483-2.096l.505.516zm-1.136-11.342c-1.778-.01-4.062.911-5.766 2.614-.65.649-1.222 1.408-1.664 2.258 1.538-1.163 3.228-1.485 5.147-.408.566-1.494 1.32-3.014 2.283-4.464zm5.204 17.5c.852-.44 1.61-1.013 2.261-1.664 1.708-1.706 2.622-4.001 2.604-5.782-1.575 1.03-3.125 1.772-4.466 2.296 1.077 1.92.764 3.614-.399 5.15zm11.312-23.956c-.428-.03-.848-.044-1.261-.044-9.338 0-14.465 7.426-16.101 13.009l4.428 4.428c5.78-1.855 12.988-6.777 12.988-15.993v-.059c-.002-.437-.019-.884-.054-1.341zm-5.946 7.956c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"
            ></path></svg
          >

          <h3 class="footer-cards__card-title">
            {t("clubs.trial_testing")}
          </h3>
        </a>

        <div class="footer-cards__card is--accent is--footer">
          <DownloadButtons
            app="coach"
            class:list={"is--footer"}
            place="Footer"
          />

          <h3 class="footer-cards__card-title">
            {t("clubs.app_coach")}
          </h3>
        </div>
        <a
          class="footer-cards__card"
          id="footer-guide-card"
          href={lang === "ru"
            ? "/documents/junicoach_step_by_step_ru.pdf"
            : "/documents/junicoach_step_by_step_en.pdf"}
          target="_blank"
        >
          <svg
            class="footer-cards__card-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="72"
            fill="none"
            viewBox="0 0 72 72"
            ><path
              fill="#231F20"
              d="M36 0C16.118 0 0 16.118 0 36s16.118 36 36 36 36-16.118 36-36S55.882 0 36 0Zm29.914 27.635L49.436 29.99a14.775 14.775 0 0 0-5.876-6.613l2.4-16.804c9.662 3.281 17.187 11.178 19.954 21.062ZM36 45.818c-5.415 0-9.818-4.403-9.818-9.818s4.403-9.818 9.818-9.818 9.818 4.403 9.818 9.818-4.403 9.818-9.818 9.818ZM27.213 6.194l2.366 16.568a14.76 14.76 0 0 0-6.91 7.01L6.14 27.411C9.077 17.233 17.064 9.19 27.213 6.194ZM6.647 46.154l16.846-2.408a14.755 14.755 0 0 0 6.307 5.597l-2.361 16.52C17.712 63.07 9.945 55.66 6.647 46.154Zm39.091 19.351-2.394-16.753a14.788 14.788 0 0 0 5.294-5.218l16.797 2.4c-3.122 9.223-10.447 16.51-19.697 19.571Z"
            ></path></svg
          >
          <div class="footer-cards__card-side">
            <h3 class="footer-cards__card-title">
              {t("clubs.guide_title")}
            </h3>
          </div>
        </a>
      </div>
    </div>
  </section>
</Layout>

<script>
  import { track } from "@amplitude/analytics-browser";

  import gsap from "gsap";
  import ScrollTrigger from "gsap/ScrollTrigger";
  gsap.registerPlugin(ScrollTrigger);

  import Swiper from "swiper";
  import { Navigation } from "swiper/modules";

  window.addEventListener("load", () => {
    //#region [Recommends]
    const swiper = new Swiper(".swiper", {
      modules: [Navigation],

      spaceBetween: 32,
      slidesPerView: "auto",
      grabCursor: true,
      navigation: {
        nextEl: ".recommends__slider-button.is--next",
        prevEl: ".recommends__slider-button.is--prev",
      },
    });

    // Ian expand
    const expandText = document.querySelector(
      ".recommends-people__card-text.is--ian"
    ) as HTMLElement;
    const expandButton = document.querySelector(
      ".recommends-people__card-text-expand"
    ) as HTMLElement;
    const showMoreLabel = document.querySelector(
      "#ian-show-more"
    ) as HTMLElement;
    const showLessLabel = document.querySelector(
      "#ian-show-less"
    ) as HTMLElement;

    let textExpanded = false;
    if (expandText && expandButton && showMoreLabel && showLessLabel) {
      expandButton.addEventListener("click", () => {
        if (!textExpanded) {
          expandText.classList.toggle("is--open");
          showMoreLabel.style.display = "none";
          showLessLabel.style.display = "block";
          textExpanded = true;

          //#region [Track open]
          expandButton.addEventListener("click", () => {
            track("Click show more");
          });
          //#endregion [Track open]
        } else {
          expandText.classList.remove("is--open");
          showMoreLabel.style.display = "block";
          showLessLabel.style.display = "none";
          textExpanded = false;
        }
      });
    }

    //#region [Track slider use]
    // Slide changed
    swiper.on("slideChange", function () {
      track("Recommends slide changed");
    });

    // Click slide
    document
      .querySelectorAll(".swiper-slide.recommends__slide")
      .forEach((slide) => {
        slide.addEventListener("click", () => {
          track("Recommends slide clicked");
        });
      });
    //#endregion [Track slider use]
    //#endregion [Recommends]

    //#region [Video]
    const myVideo = document.querySelector("video") as HTMLVideoElement;
    if (myVideo) {
      const promise = myVideo.play();
      promise.then(
        (res) => {},
        (rej) => {
          const playButton = document.querySelector(
            ".video__play-button"
          ) as HTMLElement;
          if (playButton) {
            playButton.style.display = "block";
            myVideo.style.cursor = "pointer";
            myVideo.addEventListener("click", () => {
              if (myVideo.paused) {
                myVideo.play();
                myVideo.style.cursor = "";
                playButton.style.display = "";
              }
            });
          }
        }
      );
    }

    //#endregion [Video]

    //#region [Features]
    gsap
      .timeline({
        scrollTrigger: {
          trigger: ".features__blocks",
          start: "top 80%",
          end: "bottom bottom",
          toggleActions: "play none play play", // onEnter, onLeave, onEnterBack, and onLeaveBack
          scrub: 1,
        },
      })
      .from(".features__blocks > *", {
        y: "20%",
        scale: 0.5,
        transformOrigin: "50% 100%",
        stagger: 0.1,
      });
    //#endregion [Features]

    //#region [Skills bar animation]
    const barsTl = gsap.timeline({
      scrollTrigger: {
        trigger: ".features__skills",
        start: "top 80%",
        end: "bottom center",
        scrub: 1,
      },
    });

    const numbers = document.querySelectorAll(
      ".skill-card__top .skill-card__card-digits"
    );

    barsTl
      .from(
        ".skill-card__bar-line, .skills-card__header .skill-card__bar",
        {
          width: 0,
        },
        "same"
      )
      .from(
        numbers,
        { innerText: 0, snap: { innerText: 1 }, ease: "none" },
        "same"
      );
    //#endregion [Skills bar animation]

    //#region [Track Register]
    document.querySelector("#club-register")?.addEventListener("click", () => {
      track("Click register club button");
    });
    //#endregion [Track Register]

    //#region [Track Free trail]
    document
      .querySelector("#trial-access-button")
      ?.addEventListener("click", () => {
        track("Click trial access club button");
      });

    // Footer card
    document
      .querySelector("#footer-trial-access-card")
      ?.addEventListener("click", () => {
        track("Click trial access club button", {
          place: "footer",
        });
      });
    //#endregion [Track Free trail]

    //#region [Track manual]
    document
      .querySelector("#footer-guide-card")
      ?.addEventListener("click", () => {
        track("Click guide card");
      });
    //#endregion [Track manual]

    //#region [Track Free trail]
    document
      .querySelector("#connect-club-button")
      ?.addEventListener("click", () => {
        track("Click connect club button");
      });
    //#endregion [Track Free trail]
  });

  //#region [Presentation]

  const presentationOpenModal = document.querySelector(
    ".hero__request-presentation-button"
  ) as HTMLElement;
  const presentationModal = document.querySelector(
    ".presentation-modal"
  ) as HTMLElement;
  const presentationModalClose = document.querySelector(
    ".presentation-modal__close"
  ) as HTMLElement;

  if (presentationModal && presentationModalClose && presentationOpenModal) {
    function closePresentationModal() {
      presentationModal.style.display = "none";
    }

    presentationOpenModal.addEventListener("click", () => {
      presentationModal.style.display = "flex";
    });

    presentationModalClose.addEventListener("click", () => {
      closePresentationModal();
    });

    // Close on escape and click outside
    document.addEventListener("keydown", function (event) {
      if (event.key === "Escape") {
        closePresentationModal();
      }
    });

    window.addEventListener("click", function (event) {
      if (event.target == presentationModal) {
        closePresentationModal();
      }
    });
  }

  const presentationForm = document.querySelector(
    ".presentation-modal__form"
  ) as HTMLFormElement;

  if (presentationForm) {
    presentationForm.addEventListener("submit", async (e) => {
      e.preventDefault();
      const formData = new FormData(presentationForm);

      const userEmail = formData.get("email");
      const userPhone = formData.get("phone");
      const userName = formData.get("name");
      const academyName = formData.get("academy");

      const successMessage = document.querySelector(
        ".form__success"
      ) as HTMLElement;
      const errorMessage = document.querySelector(
        ".form__error"
      ) as HTMLElement;

      try {
        const sendData = await fetch(
          "https://n8n.kobro.ru/webhook/academy-presentation",
          {
            headers: {
              "Content-Type": "application/json",
            },
            method: "POST",
            body: JSON.stringify({
              name: userName,
              phone: userPhone,
              email: userEmail,
              academyName: academyName,
              lang: document.documentElement.getAttribute("lang") || "",
            }),
          }
        );

        successMessage.style.display = "block";
        errorMessage.style.display = "none";
        presentationForm.style.display = "none";

        trackSubmit(true);
      } catch (error) {
        errorMessage.style.display = "block";
        trackSubmit(false);
      }

      function trackSubmit(status: boolean) {
        track("Submit presentation request form", {
          status: status,
          name: userName,
          phone: userPhone,
          academyName: academyName,
          email: userEmail,
        });
      }
    });
  }

  //#endregion [Presentation]
</script>
