---
//#region [Built-in components]
import { Image, getImage } from "astro:assets";
//#endregion [Built-in components]

//#region [Components]
import Layout from "@layouts/Layout.astro";
import AppButton from "@components/AppButton.astro";
import RocketLaunch from "@components/icons/RocketLaunch.astro";

//#endregion [Components]

//#region [Styles]
import "@styles/main.scss";
import "@styles/pages/academies/whuf.scss";
//#endregion [Styles]

//#region [Icons]
//#endregion [Icons]

//#region [Images]
import LogoImg from "@assets/images/academies/ian/ian-logo-min.jpg";

import WhalenImg from "@assets/images/academies/ian/WHALEN-min.png";
import WongImg from "@assets/images/academies/ian/WONG-min.png";
import MilicImg from "@assets/images/academies/ian/MILIC-min.png";

// Tests icons
import Run15upIcon from "@assets/images/ai-test/tests-icons/15m-run-up.jpg";
import PushUpsIcon from "@assets/images/ai-test/tests-icons/push-ups.jpg";
import ArrowBallIcon from "@assets/images/ai-test/tests-icons/arrow-ball.jpg";
import ArrowIcon from "@assets/images/ai-test/tests-icons/arrow.jpg";
import Dribbling15Icon from "@assets/images/ai-test/tests-icons/dribbling15-ball.jpg";
import JumpIcon from "@assets/images/ai-test/tests-icons/jump-place.jpg";
import LadderFBIcon from "@assets/images/ai-test/tests-icons/ladder-front-back.jpg";
import LadderLRIcon from "@assets/images/ai-test/tests-icons/ladder-left-right.jpg";
import KickIcon from "@assets/images/ai-test/tests-icons/monster-kick-right.jpg";
import Run15Icon from "@assets/images/ai-test/tests-icons/run-15.jpg";
import SerpentIcon from "@assets/images/ai-test/tests-icons/serpient-ball.jpg";
import OthersIcon from "@assets/images/ai-test/tests-icons/test-image-other.svg";

import VideoPosterImg from "@assets/images/talents/video-poster.jpg";
const VideoPosterImgOptimized = await getImage({
  src: VideoPosterImg,
  format: "webp",
});
//#endregion [Images]

//#region [Videos]
import IanVideoMp4 from "@assets/videos/academies/ian/junistat-overview-opt.mp4";
//#endregion [Videos]
---

<Layout title="Scouting and selecting with JuniStat" contentOnly>
  <section class="section is--hero">
    <div class="container">
      <div class="hero">
        <Image
          class="hero__logo is--circle"
          src={LogoImg}
          width={350}
          alt="Ian McClurg Learn Perform Coaching"
        />
        <h1 class="hero__headline">
          Online scouting of soccer players for trials in the UK, Germany,
          Spain, Portugal, and Italy
        </h1>
        <h2>powered by JuniStat<sup>®</sup> — smart testing system</h2>
      </div>
    </div>
  </section>
  <section class="section section-space">
    <div class="container">
      <main class="main">
        <div class="main__col guide">
          <h2>
            Step-by-Step guide to creating player’s profile and completing tests
          </h2>
          <div class="card guide__card">
            <div class="guide__circle-number h2">1</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Registration</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>Click the registration link and sign up</p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Follow the instructions sent to your email to install the
                    JuniStat app and set up your web dashboard
                  </p>
                </li>
              </ul>
              <AppButton
                as="a"
                style="mod2"
                class="reg-button"
                skew
                href="https://app.junistat.com/invite-mentors?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2FkZW15SWQiOiIzMWIwNDFlZi01MTdlLTQzZWUtYWY1Ny0zNDE4ZTRhYTY3NDkiLCJpYXQiOjE2OTg3NDEwOTMsImV4cCI6MTcwNjUxNzA5M30.2nQf5bRM9JLAQx_4rX06pfET7Mo8JkdvHz0c6wrhLW8"
                target="_blank"
              >
                <RocketLaunch />
                <span class="h3" style="font-weight: 700">Get Started</span>
              </AppButton>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">2</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Subscription payment</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    <s>$149</s> $99 annual licence payment until December 2023.
                    Make the payment through the web dashboard to unlock tests
                    within the app.
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    Once completed, the profile is shared directly with
                    professional clubs.
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div class="card guide__card">
            <div class="guide__circle-number h2">3</div>
            <div class="guide__card-content">
              <h3 class="guide__card-headline h4">Profile Enhancement</h3>
              <ul class="guide__card-list">
                <li class="guide__card-list-item">
                  <p>
                    Enhance your profile by uploading videos and providing a
                    detailed bio.
                  </p>
                </li>
                <li class="guide__card-list-item">
                  <p>
                    This information will help us make a more informed decision
                    regarding your trial
                  </p>
                </li>
              </ul>
              <div class="guide__profile-examples">
                <h3 class="guide__card-headline h4">
                  Players’ profiles eхamples
                </h3>
                <div class="guide__profile-cards">
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/98772118-c27b-4607-8891-5463dbfd8718/training"
                    target="_blank"
                  >
                    <Image src={WhalenImg} alt="Profile example" width={254} />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/3e480999-5dfc-4dd7-ad70-fab1975eb5a1/training"
                    target="_blank"
                  >
                    <Image src={WongImg} alt="Profile example" width={254} />
                  </a>
                  <a
                    class="guide__card-link"
                    href="https://app.junistat.com/player/2b88f663-a67f-45ec-b801-2afa3b9956e3/training"
                    target="_blank"
                  >
                    <Image src={MilicImg} alt="Profile example" width={254} />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main__col info">
          <div class="card info">
            <h2>
              We are actively reviewing players <strong>U14-U22</strong> who
              have registered and excelled in JuniStat tests
            </h2>

            <div class="info__video video">
              <video
                class="info__video-tag"
                poster={VideoPosterImgOptimized.src}
                style="width: 100%"
                controls
                playsinline
              >
                <source src={IanVideoMp4} type="video/mp4" />
              </video>
            </div>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <h2>
                Please complete the following 4 tests in the App and showcase
                your talent!
              </h2>
              <p>
                Once all the tests are completed, our recruitment team will
                review your profile and contact you with the feedback
              </p>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">Required tests</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image
                    class="info__test-icon"
                    src={PushUpsIcon}
                    alt="Test icon"
                  />
                  <p>Push-up’s</p>
                </div>
                <div class="info__test">
                  <Image
                    class="info__test-icon"
                    src={JumpIcon}
                    alt="Test icon"
                  />
                  <p>High jump</p>
                </div>
                <div class="info__test">
                  <Image
                    class="info__test-icon"
                    src={Run15upIcon}
                    alt="Test icon"
                  />
                  <p>15 m sprint with run up</p>
                </div>
                <div class="info__test">
                  <Image
                    class="info__test-icon"
                    src={SerpentIcon}
                    alt="Test icon"
                  />
                  <p>Serpent</p>
                </div>
              </div>
            </div>
            <div class="info__tests-wrapper">
              <h2 class="info__category-headline">Optional</h2>
              <div class="info__tests-group">
                <div class="info__test">
                  <Image
                    class="info__test-icon"
                    src={KickIcon}
                    alt="Test icon"
                  />
                  <p>Power kick</p>
                </div>
                <div class="info__test">
                  <Image
                    class="info__test-icon"
                    src={ArrowIcon}
                    alt="Test icon"
                  />
                  <p>Arrow</p>
                </div>
                <div class="info__test">
                  <Image
                    class="info__test-icon"
                    src={OthersIcon}
                    alt="Test icon"
                  />
                  <p>Other tests</p>
                </div>
              </div>
            </div>
            <AppButton
              as="a"
              style="mod2"
              skew
              class="reg-button"
              href="https://app.junistat.com/invite-mentors?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2FkZW15SWQiOiIzMWIwNDFlZi01MTdlLTQzZWUtYWY1Ny0zNDE4ZTRhYTY3NDkiLCJpYXQiOjE2OTg3NDEwOTMsImV4cCI6MTcwNjUxNzA5M30.2nQf5bRM9JLAQx_4rX06pfET7Mo8JkdvHz0c6wrhLW8"
              target="_blank"
            >
              <RocketLaunch />
              <span class="h3" style="font-weight: 700">Get Started</span>
            </AppButton>
          </div>
          <div class="card info">
            <div class="info__card-header">
              <p>
                I am here to help with any inquiries you may have. Please feel
                free to reach out <a
                  href="mailto:<EMAIL>"
                  target="_blank"><EMAIL></a
                >
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </section>
</Layout>

<script>
  import { track } from "@amplitude/analytics-browser";

  const regButtons = document.querySelectorAll(
    ".reg-button",
  ) as NodeListOf<HTMLElement>;

  regButtons.forEach((button) => {
    button.addEventListener("click", () => {
      track("Click registration button", {
        academy: "Ian",
      });
    });
  });
</script>
