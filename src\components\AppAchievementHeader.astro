---
import { Image } from "astro:assets";
import HeaderAchievementIcon from "@assets/images/header-achievement.svg";
import { useTranslations, getLangFromUrl } from "../lang/utils";

const lang: string = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<a
  href={lang === "ru"
    ? "https://www.sports.ru/football/blogs/3302868.html"
    : "https://medium.com/@junistat/junistat-named-a-finalist-in-the-barça-innovation-hub-da329d2aa128"}
  target="_blank"
>
  <div class="header__achievement">
    <div class="header__achievement_content">
      <p class="header__achievement_text">{t("Top 5 Startups 2025")}</p>
      <div class="header__achievement_icon">
        <Image src={HeaderAchievementIcon} alt="logo" />
      </div>
    </div>
  </div>
</a>

<style lang="scss">
  .header {
    &__achievement {
      cursor: pointer;
      background: #edeef1;
      width: 100%;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 50px;

      &_content {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      &_mobile_content {
        display: none;
        flex-direction: column;
        align-items: center;
        gap: 16px;
      }

      &_text {
        font-family: "Onest", sans-serif;
        color: #191b1e;
        font-weight: 600;
        font-size: 16px;
        line-height: 1;
      }
    }
  }
</style>
